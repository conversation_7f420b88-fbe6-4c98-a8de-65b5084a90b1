<?php
/**
 * 迅排设计外部API控制器
 * 提供给迅排设计服务调用的接口
 */

namespace app\api\controller;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use think\Controller;
use think\facade\Log;
use think\facade\Validate;

class PosterExternal extends Controller
{
    /**
     * 获取参数数据
     * GET /api/external/parameter-data/{dataId}
     */
    public function getParameterData()
    {
        try {
            $dataId = $this->request->param('dataId');
            
            if (empty($dataId)) {
                return $this->errorResponse(400, 'Parameter dataId is required');
            }
            
            // 查询用户数据
            $userData = PosterUserData::where('id', $dataId)->find();
            
            if (!$userData) {
                return $this->errorResponse(404, 'Parameter data not found');
            }
            
            // 获取关联的配置信息
            $config = $userData->config;
            if (!$config) {
                return $this->errorResponse(404, 'Template config not found');
            }
            
            $responseData = [
                'id' => $userData->id,
                'configId' => $userData->config_id,
                'templateId' => $config->template_id,
                'parameterValues' => $userData->parameter_values,
                'isDraft' => $userData->is_draft,
                'createdAt' => $userData->created_at,
                'updatedAt' => $userData->updated_at
            ];
            
            return $this->successResponse($responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter data error: ' . $e->getMessage());
            return $this->errorResponse(500, 'Internal server error');
        }
    }
    
    /**
     * 获取参数配置
     * GET /api/external/parameter-config/{configId}
     */
    public function getParameterConfig()
    {
        try {
            $configId = $this->request->param('configId');
            
            if (empty($configId)) {
                return $this->errorResponse(400, 'Parameter configId is required');
            }
            
            // 查询配置数据
            $config = PosterTemplateConfig::where('id', $configId)
                ->where('status', PosterTemplateConfig::STATUS_ENABLED)
                ->find();
            
            if (!$config) {
                return $this->errorResponse(404, 'Parameter config not found');
            }
            
            $responseData = [
                'id' => $config->id,
                'templateId' => $config->template_id,
                'templateTitle' => $config->template_title,
                'configName' => $config->config_name,
                'configDescription' => $config->config_description,
                'parameters' => $config->parameters,
                'createdAt' => $config->created_at,
                'updatedAt' => $config->updated_at
            ];
            
            return $this->successResponse($responseData);
            
        } catch (\Exception $e) {
            Log::error('Get parameter config error: ' . $e->getMessage());
            return $this->errorResponse(500, 'Internal server error');
        }
    }
    
    /**
     * 批量获取参数数据
     * POST /api/external/parameter-data/batch
     */
    public function getBatchParameterData()
    {
        try {
            $postData = $this->request->post();
            
            // 验证请求数据
            $validate = Validate::make([
                'dataIds' => 'require|array',
                'dataIds.*' => 'require|alphaNum'
            ]);
            
            if (!$validate->check($postData)) {
                return $this->errorResponse(400, $validate->getError());
            }
            
            $dataIds = $postData['dataIds'];
            
            if (count($dataIds) > 50) {
                return $this->errorResponse(400, 'Maximum 50 items allowed per batch request');
            }
            
            // 查询数据
            $userDataList = PosterUserData::whereIn('id', $dataIds)
                ->with('config')
                ->select();
            
            $responseData = [];
            foreach ($userDataList as $userData) {
                $config = $userData->config;
                if ($config) {
                    $responseData[] = [
                        'id' => $userData->id,
                        'configId' => $userData->config_id,
                        'templateId' => $config->template_id,
                        'parameterValues' => $userData->parameter_values,
                        'isDraft' => $userData->is_draft
                    ];
                }
            }
            
            return $this->successResponse([
                'items' => $responseData,
                'total' => count($responseData),
                'requested' => count($dataIds)
            ]);
            
        } catch (\Exception $e) {
            Log::error('Get batch parameter data error: ' . $e->getMessage());
            return $this->errorResponse(500, 'Internal server error');
        }
    }
    
    /**
     * 更新用户数据状态
     * PUT /api/external/parameter-data/{dataId}/status
     */
    public function updateParameterDataStatus()
    {
        try {
            $dataId = $this->request->param('dataId');
            $postData = $this->request->post();
            
            if (empty($dataId)) {
                return $this->errorResponse(400, 'Parameter dataId is required');
            }
            
            // 验证请求数据
            $validate = Validate::make([
                'previewUrl' => 'url',
                'generatedImageUrl' => 'url',
                'isDraft' => 'boolean'
            ]);
            
            if (!$validate->check($postData)) {
                return $this->errorResponse(400, $validate->getError());
            }
            
            // 查询用户数据
            $userData = PosterUserData::find($dataId);
            if (!$userData) {
                return $this->errorResponse(404, 'Parameter data not found');
            }
            
            // 更新数据
            $updateData = [];
            if (isset($postData['previewUrl'])) {
                $updateData['preview_url'] = $postData['previewUrl'];
            }
            if (isset($postData['generatedImageUrl'])) {
                $updateData['generated_image_url'] = $postData['generatedImageUrl'];
            }
            if (isset($postData['isDraft'])) {
                $updateData['is_draft'] = $postData['isDraft'];
            }
            
            if (!empty($updateData)) {
                $userData->updateUserData($updateData);
            }
            
            return $this->successResponse([
                'id' => $userData->id,
                'updated' => true,
                'updatedAt' => $userData->updated_at
            ]);
            
        } catch (\Exception $e) {
            Log::error('Update parameter data status error: ' . $e->getMessage());
            return $this->errorResponse(500, 'Internal server error');
        }
    }
    
    /**
     * 健康检查
     * GET /api/external/health
     */
    public function health()
    {
        try {
            // 检查数据库连接
            $dbStatus = $this->checkDatabaseConnection();
            
            // 检查配置表
            $configCount = PosterTemplateConfig::count();
            
            // 检查用户数据表
            $userDataCount = PosterUserData::count();
            
            $responseData = [
                'status' => 'ok',
                'timestamp' => date('c'),
                'database' => $dbStatus ? 'connected' : 'disconnected',
                'statistics' => [
                    'total_configs' => $configCount,
                    'total_user_data' => $userDataCount
                ],
                'version' => '1.0.0'
            ];
            
            return $this->successResponse($responseData);
            
        } catch (\Exception $e) {
            Log::error('Health check error: ' . $e->getMessage());
            return $this->errorResponse(503, 'Service unavailable');
        }
    }
    
    /**
     * 获取API使用统计
     * GET /api/external/stats
     */
    public function getStats()
    {
        try {
            $startDate = $this->request->param('start_date');
            $endDate = $this->request->param('end_date');
            
            // 默认查询最近7天的数据
            if (!$startDate) {
                $startDate = date('Y-m-d', strtotime('-7 days'));
            }
            if (!$endDate) {
                $endDate = date('Y-m-d');
            }
            
            // 获取统计数据
            $stats = [
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ],
                'configs' => [
                    'total' => PosterTemplateConfig::count(),
                    'enabled' => PosterTemplateConfig::where('status', 1)->count()
                ],
                'user_data' => [
                    'total' => PosterUserData::count(),
                    'drafts' => PosterUserData::where('is_draft', true)->count(),
                    'published' => PosterUserData::where('is_draft', false)->count()
                ],
                'recent_activity' => [
                    'new_data_today' => PosterUserData::whereTime('created_at', 'today')->count(),
                    'new_data_this_week' => PosterUserData::whereTime('created_at', 'week')->count()
                ]
            ];
            
            return $this->successResponse($stats);
            
        } catch (\Exception $e) {
            Log::error('Get stats error: ' . $e->getMessage());
            return $this->errorResponse(500, 'Internal server error');
        }
    }
    
    /**
     * 检查数据库连接
     */
    private function checkDatabaseConnection()
    {
        try {
            \think\Db::query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 成功响应
     */
    private function successResponse($data = null)
    {
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $data
        ]);
    }
    
    /**
     * 错误响应
     */
    private function errorResponse($code, $message, $data = null)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'error' => [
                'reason' => $message,
                'timestamp' => date('c')
            ],
            'data' => $data
        ], $code);
    }
}
