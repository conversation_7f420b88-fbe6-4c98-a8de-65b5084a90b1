#!/bin/bash

# 动态参数模板系统部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev|test|prod
# 操作: install|update|migrate|test|backup|rollback

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_NAME="动态参数模板系统"
PROJECT_DIR=$(pwd)
BACKUP_DIR="$PROJECT_DIR/backups"
LOG_FILE="$PROJECT_DIR/deploy.log"

# 环境配置
ENV=${1:-dev}
ACTION=${2:-install}

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# 检查环境
check_environment() {
    log "检查部署环境..."
    
    # 检查PHP版本
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$PHP_VERSION >= 7.0" | bc -l) -eq 0 ]]; then
        error "PHP版本必须 >= 7.0，当前版本: $PHP_VERSION"
    fi
    info "PHP版本: $PHP_VERSION ✓"
    
    # 检查必需的PHP扩展
    REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "json" "curl" "mbstring" "openssl")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "$ext"; then
            error "缺少PHP扩展: $ext"
        fi
    done
    info "PHP扩展检查完成 ✓"
    
    # 检查Composer
    if ! command -v composer &> /dev/null; then
        error "Composer未安装"
    fi
    info "Composer已安装 ✓"
    
    # 检查数据库连接
    if [[ -f ".env" ]]; then
        source .env
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" &> /dev/null || error "数据库连接失败"
        info "数据库连接正常 ✓"
    fi
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    # 安装Composer依赖
    if [[ -f "composer.json" ]]; then
        composer install --no-dev --optimize-autoloader
        info "Composer依赖安装完成 ✓"
    fi
    
    # 安装测试依赖（开发环境）
    if [[ "$ENV" == "dev" ]]; then
        composer install --dev
        info "开发依赖安装完成 ✓"
    fi
}

# 配置环境
setup_environment() {
    log "配置环境..."
    
    # 复制环境配置文件
    if [[ ! -f ".env" ]]; then
        cp .env.example .env
        warning "请编辑 .env 文件配置数据库和API信息"
    fi
    
    # 创建必要目录
    DIRECTORIES=("runtime" "uploads" "uploads/poster" "logs" "backups")
    for dir in "${DIRECTORIES[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            chmod 755 "$dir"
        fi
    done
    info "目录创建完成 ✓"
    
    # 设置权限
    chmod -R 755 runtime/
    chmod -R 755 uploads/
    chmod -R 755 logs/
    info "权限设置完成 ✓"
}

# 数据库迁移
migrate_database() {
    log "执行数据库迁移..."
    
    # 检查数据库是否存在
    source .env
    DB_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SHOW DATABASES LIKE '$DB_NAME';" | grep "$DB_NAME" || true)
    
    if [[ -z "$DB_EXISTS" ]]; then
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        info "数据库 $DB_NAME 创建完成 ✓"
    fi
    
    # 执行迁移脚本
    if [[ -f "database/migrations/create_poster_tables.sql" ]]; then
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < database/migrations/create_poster_tables.sql
        info "数据库表创建完成 ✓"
    fi
    
    # 创建测试数据库（开发环境）
    if [[ "$ENV" == "dev" && -n "$TEST_DB_NAME" ]]; then
        TEST_DB_EXISTS=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SHOW DATABASES LIKE '$TEST_DB_NAME';" | grep "$TEST_DB_NAME" || true)
        if [[ -z "$TEST_DB_EXISTS" ]]; then
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE $TEST_DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            info "测试数据库 $TEST_DB_NAME 创建完成 ✓"
        fi
    fi
}

# 运行测试
run_tests() {
    log "运行测试..."
    
    if [[ ! -d "test" ]]; then
        warning "测试目录不存在，跳过测试"
        return
    fi
    
    cd test
    
    # 运行单元测试
    if command -v phpunit &> /dev/null; then
        phpunit --testsuite=Unit
        info "单元测试完成 ✓"
        
        # 运行集成测试（仅开发环境）
        if [[ "$ENV" == "dev" ]]; then
            phpunit --testsuite=Integration
            info "集成测试完成 ✓"
        fi
    else
        warning "PHPUnit未安装，跳过测试"
    fi
    
    cd ..
}

# 备份数据
backup_data() {
    log "备份数据..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
    fi
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql"
    
    source .env
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_FILE"
    
    # 压缩备份文件
    gzip "$BACKUP_FILE"
    
    info "数据备份完成: $BACKUP_FILE.gz ✓"
    
    # 清理旧备份（保留最近10个）
    cd "$BACKUP_DIR"
    ls -t backup_*.sql.gz | tail -n +11 | xargs -r rm
    cd "$PROJECT_DIR"
}

# 清理缓存
clear_cache() {
    log "清理缓存..."
    
    # 清理ThinkPHP缓存
    if [[ -d "runtime/cache" ]]; then
        rm -rf runtime/cache/*
        info "应用缓存清理完成 ✓"
    fi
    
    # 清理日志文件（保留最近7天）
    if [[ -d "runtime/log" ]]; then
        find runtime/log -name "*.log" -mtime +7 -delete
        info "日志清理完成 ✓"
    fi
    
    # 清理临时文件
    if [[ -d "runtime/temp" ]]; then
        rm -rf runtime/temp/*
        info "临时文件清理完成 ✓"
    fi
}

# 优化性能
optimize_performance() {
    log "优化性能..."
    
    # 生成类映射文件
    if command -v composer &> /dev/null; then
        composer dump-autoload --optimize --no-dev
        info "自动加载优化完成 ✓"
    fi
    
    # 预编译模板（如果使用模板引擎）
    # php think template:compile
    
    info "性能优化完成 ✓"
}

# 主函数
main() {
    log "开始部署 $PROJECT_NAME ($ENV 环境)"
    
    case $ACTION in
        "install")
            check_environment
            install_dependencies
            setup_environment
            migrate_database
            clear_cache
            optimize_performance
            if [[ "$ENV" == "dev" ]]; then
                run_tests
            fi
            ;;
        "update")
            backup_data
            install_dependencies
            migrate_database
            clear_cache
            optimize_performance
            ;;
        "migrate")
            migrate_database
            ;;
        "test")
            run_tests
            ;;
        "backup")
            backup_data
            ;;
        "rollback")
            # 实现回滚逻辑
            warning "回滚功能待实现"
            ;;
        *)
            error "未知操作: $ACTION"
            ;;
    esac
    
    log "$PROJECT_NAME 部署完成！"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  test    测试环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  install   全新安装"
    echo "  update    更新部署"
    echo "  migrate   数据库迁移"
    echo "  test      运行测试"
    echo "  backup    备份数据"
    echo "  rollback  回滚版本"
    echo ""
    echo "示例:"
    echo "  $0 dev install    # 开发环境全新安装"
    echo "  $0 prod update    # 生产环境更新"
    echo "  $0 test test      # 运行测试"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
    exit 0
fi

# 执行主函数
main
