<?php
/**
 * 外部API集成测试
 */

namespace Test\Integration;

use PHPUnit\Framework\TestCase;
use Test\Tools\TestHelper;

class ExternalApiTest extends TestCase
{
    private $baseUrl;
    private $apiKey;
    private $testData;
    
    protected function setUp(): void
    {
        // 初始化测试环境
        TestHelper::initTestEnvironment();
        TestHelper::insertTestData();
        
        $this->baseUrl = 'http://localhost/api/external';
        $this->apiKey = 'test-api-key-for-development';
        $this->testData = include __DIR__ . '/../config/test_data.php';
    }
    
    protected function tearDown(): void
    {
        // 清理测试数据
        TestHelper::cleanTestData();
    }
    
    /**
     * 测试健康检查接口
     */
    public function testHealthCheck()
    {
        $response = $this->makeRequest('GET', '/health');
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        $this->assertEquals('success', $response['data']['message']);
        $this->assertArrayHasKey('status', $response['data']['data']);
        $this->assertEquals('ok', $response['data']['data']['status']);
    }
    
    /**
     * 测试获取参数数据接口
     */
    public function testGetParameterData()
    {
        $dataId = 'test-data-001';
        $response = $this->makeRequest('GET', "/parameter-data/{$dataId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($dataId, $data['id']);
        $this->assertArrayHasKey('configId', $data);
        $this->assertArrayHasKey('templateId', $data);
        $this->assertArrayHasKey('parameterValues', $data);
    }
    
    /**
     * 测试获取不存在的参数数据
     */
    public function testGetNonExistentParameterData()
    {
        $dataId = 'non-existent-data';
        $response = $this->makeRequest('GET', "/parameter-data/{$dataId}");
        
        $this->assertEquals(404, $response['http_code']);
        $this->assertEquals(404, $response['data']['code']);
        $this->assertEquals('Parameter data not found', $response['data']['message']);
    }
    
    /**
     * 测试获取参数配置接口
     */
    public function testGetParameterConfig()
    {
        $configId = 'test-config-001';
        $response = $this->makeRequest('GET', "/parameter-config/{$configId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($configId, $data['id']);
        $this->assertArrayHasKey('templateId', $data);
        $this->assertArrayHasKey('parameters', $data);
        $this->assertIsArray($data['parameters']);
    }
    
    /**
     * 测试批量获取参数数据接口
     */
    public function testBatchGetParameterData()
    {
        $dataIds = ['test-data-001', 'test-data-002'];
        $postData = ['dataIds' => $dataIds];
        
        $response = $this->makeRequest('POST', '/parameter-data/batch', $postData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('items', $data);
        $this->assertArrayHasKey('total', $data);
        $this->assertArrayHasKey('requested', $data);
        $this->assertEquals(count($dataIds), $data['requested']);
        $this->assertLessThanOrEqual($data['requested'], $data['total']);
    }
    
    /**
     * 测试批量获取参数数据 - 超出限制
     */
    public function testBatchGetParameterDataExceedsLimit()
    {
        // 创建超过50个的数据ID数组
        $dataIds = array_map(function($i) {
            return "test-data-{$i}";
        }, range(1, 51));
        
        $postData = ['dataIds' => $dataIds];
        $response = $this->makeRequest('POST', '/parameter-data/batch', $postData);
        
        $this->assertEquals(400, $response['http_code']);
        $this->assertEquals(400, $response['data']['code']);
        $this->assertStringContains('Maximum 50 items', $response['data']['message']);
    }
    
    /**
     * 测试更新参数数据状态接口
     */
    public function testUpdateParameterDataStatus()
    {
        $dataId = 'test-data-001';
        $updateData = [
            'previewUrl' => 'http://localhost:7001/preview/test-preview',
            'generatedImageUrl' => 'http://localhost:7001/images/test-image.jpg',
            'isDraft' => false
        ];
        
        $response = $this->makeRequest('PUT', "/parameter-data/{$dataId}/status", $updateData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($dataId, $data['id']);
        $this->assertTrue($data['updated']);
        $this->assertArrayHasKey('updatedAt', $data);
    }
    
    /**
     * 测试获取统计信息接口
     */
    public function testGetStats()
    {
        $response = $this->makeRequest('GET', '/stats');
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('date_range', $data);
        $this->assertArrayHasKey('configs', $data);
        $this->assertArrayHasKey('user_data', $data);
        $this->assertArrayHasKey('recent_activity', $data);
        
        // 验证统计数据结构
        $this->assertArrayHasKey('total', $data['configs']);
        $this->assertArrayHasKey('enabled', $data['configs']);
        $this->assertArrayHasKey('total', $data['user_data']);
        $this->assertArrayHasKey('drafts', $data['user_data']);
        $this->assertArrayHasKey('published', $data['user_data']);
    }
    
    /**
     * 测试API认证 - 无效密钥
     */
    public function testInvalidApiKey()
    {
        $response = $this->makeRequest('GET', '/health', [], 'invalid-api-key');
        
        $this->assertEquals(401, $response['http_code']);
        $this->assertEquals(401, $response['data']['code']);
        $this->assertStringContains('Invalid API key', $response['data']['message']);
    }
    
    /**
     * 测试API认证 - 缺少密钥
     */
    public function testMissingApiKey()
    {
        $response = $this->makeRequest('GET', '/health', [], null);
        
        $this->assertEquals(401, $response['http_code']);
        $this->assertEquals(401, $response['data']['code']);
    }
    
    /**
     * 测试请求频率限制
     */
    public function testRateLimit()
    {
        // 这个测试需要根据实际的频率限制配置来调整
        // 这里只是示例，实际测试可能需要模拟大量请求
        
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->makeRequest('GET', '/health');
        }
        
        // 验证前几个请求都成功
        foreach (array_slice($responses, 0, 3) as $response) {
            $this->assertEquals(200, $response['http_code']);
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $path, $data = [], $apiKey = null)
    {
        $url = $this->baseUrl . $path;
        $apiKey = $apiKey ?? $this->apiKey;
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($apiKey) {
            $headers[] = 'Authorization: Bearer ' . $apiKey;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method
        ]);
        
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->fail("cURL Error: {$error}");
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'http_code' => $httpCode,
            'data' => $decodedResponse,
            'raw_response' => $response
        ];
    }
}
