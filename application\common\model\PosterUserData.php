<?php
/**
 * 用户参数数据模型
 */

namespace app\common\model;

use think\Model;

class PosterUserData extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'poster_user_data';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'                    => 'string',
        'config_id'             => 'string',
        'user_id'               => 'string',
        'session_id'            => 'string',
        'parameter_values'      => 'json',
        'is_draft'              => 'boolean',
        'preview_url'           => 'string',
        'generated_image_url'   => 'string',
        'created_at'            => 'timestamp',
        'updated_at'            => 'timestamp',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // JSON字段
    protected $json = ['parameter_values'];
    
    // 字段类型转换
    protected $type = [
        'parameter_values' => 'array',
        'is_draft'         => 'boolean',
        'created_at'       => 'timestamp',
        'updated_at'       => 'timestamp',
    ];
    
    /**
     * 关联模板配置
     */
    public function config()
    {
        return $this->belongsTo(PosterTemplateConfig::class, 'config_id', 'id');
    }
    
    /**
     * 关联生成记录
     */
    public function generationRecords()
    {
        return $this->hasMany(PosterGenerationRecord::class, 'data_id', 'id');
    }
    
    /**
     * 创建用户数据
     */
    public static function createUserData($data)
    {
        $userData = new self();
        $userData->id = self::generateId();
        $userData->config_id = $data['config_id'];
        $userData->user_id = $data['user_id'] ?? null;
        $userData->session_id = $data['session_id'] ?? null;
        $userData->parameter_values = $data['parameter_values'];
        $userData->is_draft = $data['is_draft'] ?? true;
        $userData->preview_url = $data['preview_url'] ?? null;
        $userData->generated_image_url = $data['generated_image_url'] ?? null;
        
        return $userData->save() ? $userData : false;
    }
    
    /**
     * 更新用户数据
     */
    public function updateUserData($data)
    {
        $allowedFields = [
            'parameter_values', 'is_draft', 'preview_url', 'generated_image_url'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $this->$field = $data[$field];
            }
        }
        
        return $this->save();
    }
    
    /**
     * 根据用户ID获取数据
     */
    public static function getByUserId($userId, $isDraft = null)
    {
        $where = ['user_id' => $userId];
        if ($isDraft !== null) {
            $where['is_draft'] = $isDraft;
        }
        
        return self::where($where)
            ->with('config')
            ->order('created_at', 'desc')
            ->select();
    }
    
    /**
     * 根据会话ID获取数据
     */
    public static function getBySessionId($sessionId, $isDraft = null)
    {
        $where = ['session_id' => $sessionId];
        if ($isDraft !== null) {
            $where['is_draft'] = $isDraft;
        }
        
        return self::where($where)
            ->with('config')
            ->order('created_at', 'desc')
            ->select();
    }
    
    /**
     * 根据配置ID获取数据
     */
    public static function getByConfigId($configId, $limit = null)
    {
        $query = self::where('config_id', $configId)
            ->order('created_at', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->select();
    }
    
    /**
     * 获取完整数据（包含配置信息）
     */
    public function getFullData()
    {
        $config = $this->config;
        if (!$config) {
            return null;
        }
        
        return [
            'id' => $this->id,
            'configId' => $this->config_id,
            'templateId' => $config->template_id,
            'templateTitle' => $config->template_title,
            'parameterValues' => $this->parameter_values,
            'isDraft' => $this->is_draft,
            'previewUrl' => $this->preview_url,
            'generatedImageUrl' => $this->generated_image_url,
            'createdAt' => $this->created_at,
            'updatedAt' => $this->updated_at
        ];
    }
    
    /**
     * 设置为非草稿状态
     */
    public function publish()
    {
        $this->is_draft = false;
        return $this->save();
    }
    
    /**
     * 设置预览URL
     */
    public function setPreviewUrl($url)
    {
        $this->preview_url = $url;
        return $this->save();
    }
    
    /**
     * 设置生成的图片URL
     */
    public function setGeneratedImageUrl($url)
    {
        $this->generated_image_url = $url;
        return $this->save();
    }
    
    /**
     * 生成唯一ID
     */
    private static function generateId()
    {
        return 'data_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * 验证参数值格式
     */
    public function validateParameterValues($values, $config)
    {
        if (!is_array($values) || !$config) {
            return false;
        }
        
        $parameters = $config->parameters;
        if (!is_array($parameters)) {
            return false;
        }
        
        // 检查必填参数
        foreach ($parameters as $param) {
            if ($param['isRequired'] && $param['isEnabled']) {
                $paramName = $param['parameterName'];
                if (!isset($values[$paramName]) || empty($values[$paramName])) {
                    return false;
                }
            }
        }
        
        return true;
    }
}
