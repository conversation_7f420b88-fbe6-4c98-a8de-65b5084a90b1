<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="bootstrap.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./integration</directory>
        </testsuite>
        <testsuite name="Functional">
            <directory suffix="Test.php">./functional</directory>
        </testsuite>
        <testsuite name="Performance">
            <directory suffix="Test.php">./performance</directory>
        </testsuite>
    </testsuites>

    <coverage cacheDirectory=".phpunit.cache/code-coverage"
              processUncoveredFiles="true">
        <include>
            <directory suffix=".php">../application/common/model</directory>
            <directory suffix=".php">../application/common/service</directory>
            <directory suffix=".php">../application/admin/logic</directory>
            <directory suffix=".php">../application/api/logic</directory>
            <directory suffix=".php">../application/admin/controller</directory>
            <directory suffix=".php">../application/api/controller</directory>
        </include>
        <exclude>
            <directory>../application/common/model/Base.php</directory>
            <file>../application/admin/controller/AdminBase.php</file>
            <file>../application/api/controller/ApiBase.php</file>
        </exclude>
        <report>
            <html outputDirectory="coverage-html"/>
            <text outputFile="coverage.txt"/>
            <clover outputFile="coverage.xml"/>
        </report>
    </coverage>

    <logging>
        <junit outputFile="test-results.xml"/>
        <testdoxHtml outputFile="testdox.html"/>
        <testdoxText outputFile="testdox.txt"/>
    </logging>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_CONNECTION" value="mysql"/>
        <env name="DB_HOST" value="127.0.0.1"/>
        <env name="DB_PORT" value="3306"/>
        <env name="DB_DATABASE" value="likeshop_test"/>
        <env name="DB_USERNAME" value="root"/>
        <env name="DB_PASSWORD" value="root"/>
        <env name="POSTER_API_URL" value="http://localhost:7001"/>
        <env name="POSTER_USE_MOCK" value="true"/>
        <env name="POSTER_TEST_API_KEY" value="test-api-key-for-development"/>
    </php>

    <groups>
        <exclude>
            <group>slow</group>
            <group>external</group>
        </exclude>
    </groups>
</phpunit>
