<?php
/**
 * 参数配置逻辑层
 */

namespace app\admin\logic;

use app\common\model\PosterTemplateConfig;
use think\Db;

class PosterConfigLogic
{
    /**
     * 获取配置列表
     */
    public static function getConfigList($get)
    {
        $page = $get['page'] ?? 1;
        $limit = $get['limit'] ?? 10;
        $keyword = $get['keyword'] ?? '';
        $templateId = $get['template_id'] ?? '';
        $status = $get['status'] ?? '';
        
        $where = [];
        
        if (!empty($keyword)) {
            $where[] = ['config_name|template_title', 'like', '%' . $keyword . '%'];
        }
        
        if (!empty($templateId)) {
            $where[] = ['template_id', '=', $templateId];
        }
        
        if ($status !== '') {
            $where[] = ['status', '=', $status];
        }
        
        $count = PosterTemplateConfig::where($where)->count();
        
        $lists = PosterTemplateConfig::where($where)
            ->page($page, $limit)
            ->order('created_at', 'desc')
            ->select();
        
        foreach ($lists as &$item) {
            $item['parameter_count'] = is_array($item['parameters']) ? count($item['parameters']) : 0;
            $item['status_text'] = $item['status'] == 1 ? '启用' : '禁用';
            $item['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($item['created_at']));
        }
        
        return [
            'count' => $count,
            'lists' => $lists
        ];
    }
    
    /**
     * 添加配置
     */
    public static function addConfig($data, $createdBy)
    {
        try {
            $configData = [
                'template_id' => $data['template_id'],
                'template_title' => $data['template_title'] ?? '',
                'config_name' => $data['config_name'],
                'config_description' => $data['config_description'] ?? '',
                'parameters' => $data['parameters'] ?? [],
                'created_by' => $createdBy
            ];
            
            return PosterTemplateConfig::createConfig($configData);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 编辑配置
     */
    public static function editConfig($data)
    {
        try {
            $config = PosterTemplateConfig::find($data['id']);
            if (!$config) {
                return false;
            }
            
            return $config->updateConfig($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 删除配置
     */
    public static function delConfig($id)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                return false;
            }
            
            // 检查是否有关联的用户数据
            $userDataCount = Db::name('poster_user_data')
                ->where('config_id', $id)
                ->count();
            
            if ($userDataCount > 0) {
                throw new \Exception('该配置下还有用户数据，无法删除');
            }
            
            return $config->delete();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取配置详情
     */
    public static function getConfigDetail($id)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                return null;
            }
            
            $data = $config->toArray();
            $data['parameter_stats'] = $config->getParameterStats();
            
            // 获取关联的用户数据统计
            $data['user_data_stats'] = [
                'total' => Db::name('poster_user_data')->where('config_id', $id)->count(),
                'drafts' => Db::name('poster_user_data')->where('config_id', $id)->where('is_draft', 1)->count(),
                'published' => Db::name('poster_user_data')->where('config_id', $id)->where('is_draft', 0)->count()
            ];
            
            return $data;
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 启用/禁用配置
     */
    public static function toggleStatus($id, $status)
    {
        try {
            $config = PosterTemplateConfig::find($id);
            if (!$config) {
                return false;
            }
            
            $config->status = $status;
            return $config->save();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 复制配置
     */
    public static function copyConfig($id, $createdBy)
    {
        try {
            $originalConfig = PosterTemplateConfig::find($id);
            if (!$originalConfig) {
                return false;
            }
            
            $newConfigData = [
                'template_id' => $originalConfig->template_id,
                'template_title' => $originalConfig->template_title,
                'config_name' => $originalConfig->config_name . '_副本',
                'config_description' => $originalConfig->config_description,
                'parameters' => $originalConfig->parameters,
                'created_by' => $createdBy
            ];
            
            return PosterTemplateConfig::createConfig($newConfigData);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取配置选项（用于下拉选择）
     */
    public static function getConfigOptions($templateId = null)
    {
        $where = ['status' => PosterTemplateConfig::STATUS_ENABLED];
        if ($templateId) {
            $where['template_id'] = $templateId;
        }
        
        $configs = PosterTemplateConfig::where($where)
            ->field('id,config_name,template_title')
            ->order('created_at', 'desc')
            ->select();
        
        $options = [];
        foreach ($configs as $config) {
            $options[] = [
                'value' => $config['id'],
                'label' => $config['config_name'] . ' (' . $config['template_title'] . ')'
            ];
        }
        
        return $options;
    }
    
    /**
     * 验证参数配置
     */
    public static function validateParameters($parameters)
    {
        if (!is_array($parameters)) {
            return false;
        }
        
        $requiredFields = [
            'elementUuid', 'parameterName', 'parameterLabel', 
            'parameterType', 'isRequired', 'isEnabled'
        ];
        
        foreach ($parameters as $param) {
            foreach ($requiredFields as $field) {
                if (!isset($param[$field])) {
                    return false;
                }
            }
            
            // 验证参数类型
            $allowedTypes = ['text', 'textarea', 'email', 'phone', 'url', 'number'];
            if (!in_array($param['parameterType'], $allowedTypes)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取配置统计信息
     */
    public static function getConfigStats()
    {
        try {
            $stats = [
                'total' => PosterTemplateConfig::count(),
                'enabled' => PosterTemplateConfig::where('status', 1)->count(),
                'disabled' => PosterTemplateConfig::where('status', 0)->count(),
                'recent' => PosterTemplateConfig::whereTime('created_at', 'week')->count()
            ];
            
            // 按模板分组统计
            $templateStats = Db::name('poster_template_configs')
                ->field('template_id, template_title, count(*) as config_count')
                ->group('template_id')
                ->order('config_count', 'desc')
                ->limit(10)
                ->select();
            
            $stats['top_templates'] = $templateStats;
            
            return $stats;
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'recent' => 0,
                'top_templates' => []
            ];
        }
    }
}
