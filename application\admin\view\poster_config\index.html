{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>参数配置管理</span>
        <div class="layui-btn-group fr">
            <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="location.href='{:url(\"poster_config/add\")}'">
                <i class="layui-icon layui-icon-add-1"></i> 添加配置
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <div class="layui-form layui-form-pane search-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-inline">
                        <input type="text" name="keyword" placeholder="配置名称/模板标题" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">模板ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="template_id" placeholder="模板ID" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="configTable" lay-filter="configTable"></table>
    </div>
</div>

<script type="text/html" id="configTableToolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i> 刷新
        </button>
    </div>
</script>

<script type="text/html" id="configTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="copy">复制</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
</script>

<script type="text/html" id="statusTpl">
    <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusSwitch" {{d.status == 1 ? 'checked' : ''}}>
</script>

<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    var tableIns = table.render({
        elem: '#configTable',
        url: '{:url("poster_config/index")}',
        toolbar: '#configTableToolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {field: 'id', title: 'ID', width: 120, fixed: 'left'},
            {field: 'config_name', title: '配置名称', width: 200},
            {field: 'template_title', title: '模板标题', width: 200},
            {field: 'template_id', title: '模板ID', width: 120},
            {field: 'parameter_count', title: '参数数量', width: 100, align: 'center'},
            {field: 'status', title: '状态', width: 100, templet: '#statusTpl', align: 'center'},
            {field: 'created_at_formatted', title: '创建时间', width: 160},
            {field: 'created_by', title: '创建者', width: 120},
            {title: '操作', width: 240, toolbar: '#configTableBar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 搜索
    form.on('submit(search)', function(data){
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 状态切换
    form.on('switch(statusSwitch)', function(obj){
        var status = obj.elem.checked ? 1 : 0;
        var id = obj.value;
        
        layer.load();
        $.post('{:url("poster_config/toggleStatus")}', {id: id, status: status}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                layer.msg('状态更新成功', {icon: 1});
            } else {
                layer.msg(res.msg || '状态更新失败', {icon: 2});
                // 恢复开关状态
                obj.elem.checked = !obj.elem.checked;
                form.render('checkbox');
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
            // 恢复开关状态
            obj.elem.checked = !obj.elem.checked;
            form.render('checkbox');
        });
    });

    // 头工具栏事件
    table.on('toolbar(configTable)', function(obj){
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(configTable)', function(obj){
        var data = obj.data;
        switch(obj.event){
            case 'detail':
                showConfigDetail(data.id);
                break;
            case 'edit':
                location.href = '{:url("poster_config/edit")}?id=' + data.id;
                break;
            case 'copy':
                copyConfig(data.id);
                break;
            case 'del':
                deleteConfig(data.id);
                break;
        }
    });

    // 显示配置详情
    function showConfigDetail(configId) {
        layer.load();
        $.get('{:url("poster_config/detail")}', {id: configId}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                var config = res.data;
                var content = '<div class="config-detail">';
                content += '<h3>' + config.config_name + '</h3>';
                content += '<p><strong>配置ID:</strong> ' + config.id + '</p>';
                content += '<p><strong>模板ID:</strong> ' + config.template_id + '</p>';
                content += '<p><strong>模板标题:</strong> ' + config.template_title + '</p>';
                content += '<p><strong>描述:</strong> ' + (config.config_description || '-') + '</p>';
                content += '<p><strong>参数统计:</strong></p>';
                content += '<ul>';
                content += '<li>总参数数: ' + config.parameter_stats.total + '</li>';
                content += '<li>启用参数: ' + config.parameter_stats.enabled + '</li>';
                content += '<li>必填参数: ' + config.parameter_stats.required + '</li>';
                content += '</ul>';
                content += '<p><strong>用户数据统计:</strong></p>';
                content += '<ul>';
                content += '<li>总数据量: ' + config.user_data_stats.total + '</li>';
                content += '<li>草稿数据: ' + config.user_data_stats.drafts + '</li>';
                content += '<li>已发布: ' + config.user_data_stats.published + '</li>';
                content += '</ul>';
                content += '</div>';
                
                layer.open({
                    type: 1,
                    title: '配置详情',
                    content: content,
                    area: ['600px', '500px'],
                    shadeClose: true
                });
            } else {
                layer.msg(res.msg || '获取详情失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
    }

    // 复制配置
    function copyConfig(configId) {
        layer.confirm('确定要复制这个配置吗？', function(index){
            layer.load();
            $.post('{:url("poster_config/copy")}', {id: configId}, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('复制成功', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '复制失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
            layer.close(index);
        });
    }

    // 删除配置
    function deleteConfig(configId) {
        layer.confirm('确定要删除这个配置吗？删除后不可恢复！', function(index){
            layer.load();
            $.post('{:url("poster_config/del")}', {id: configId}, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('删除成功', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '删除失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
            layer.close(index);
        });
    }
});
</script>

<style>
.config-detail ul {
    margin: 10px 0;
    padding-left: 20px;
}
.config-detail li {
    margin: 5px 0;
}
.search-form {
    margin-bottom: 15px;
}
.search-form .layui-form-item {
    margin-bottom: 0;
}
</style>
</div>
