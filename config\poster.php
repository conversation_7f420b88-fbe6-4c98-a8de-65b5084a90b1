<?php
/**
 * 动态参数模板系统配置
 */

use think\facade\Env;

return [
    // 迅排设计API配置
    'api_url' => Env::get('poster.api_url', 'http://localhost:7001'),
    'timeout' => Env::get('poster.timeout', 30),
    'retry_times' => Env::get('poster.retry_times', 3),
    'retry_delay' => Env::get('poster.retry_delay', 1000),
    
    // 缓存配置
    'cache_enabled' => Env::get('poster.cache_enabled', true),
    'cache_ttl' => Env::get('poster.cache_ttl', 600),
    
    // 外部API配置
    'api_key' => Env::get('poster.api_key', ''),
    'test_api_key' => Env::get('poster.test_api_key', 'test-api-key-for-development'),
    'api_keys' => [
        // 可以在这里添加额外的API密钥
    ],
    
    // 频率限制配置
    'rate_limit' => Env::get('poster.rate_limit', 1000), // 每小时请求次数
    'rate_limit_window' => Env::get('poster.rate_limit_window', 3600), // 限制窗口（秒）
    
    // 日志配置
    'log_enabled' => Env::get('poster.log_enabled', true),
    'log_level' => Env::get('poster.log_level', 'info'),
    
    // 文件上传配置
    'upload' => [
        'max_size' => Env::get('poster.upload_max_size', 10485760), // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif'],
        'upload_path' => 'uploads/poster/',
    ],
    
    // 图片生成配置
    'generation' => [
        'default_width' => 1242,
        'default_height' => 2208,
        'default_quality' => 0.9,
        'max_width' => 4000,
        'max_height' => 4000,
        'min_quality' => 0.1,
        'max_quality' => 1.0,
    ],
    
    // 批量处理配置
    'batch' => [
        'max_items' => 50,
        'timeout' => 300, // 5分钟
    ],
    
    // 数据清理配置
    'cleanup' => [
        'auto_cleanup' => true,
        'draft_retention_days' => 7, // 草稿保留天数
        'record_retention_days' => 30, // 生成记录保留天数
    ],
    
    // 安全配置
    'security' => [
        'enable_ip_whitelist' => false,
        'ip_whitelist' => [
            '127.0.0.1',
            '::1'
        ],
        'enable_cors' => true,
        'cors_origins' => ['*'],
    ],
    
    // 监控配置
    'monitoring' => [
        'enable_metrics' => true,
        'metrics_retention_days' => 30,
        'alert_thresholds' => [
            'error_rate' => 0.05, // 5%错误率
            'response_time' => 5000, // 5秒响应时间
        ]
    ]
];
