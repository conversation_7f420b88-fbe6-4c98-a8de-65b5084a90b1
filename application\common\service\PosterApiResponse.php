<?php
/**
 * 迅排设计API响应处理类
 */

namespace app\common\service;

class PosterApiResponse
{
    private $rawResponse;
    private $code;
    private $message;
    private $data;
    
    public function __construct($response)
    {
        $this->rawResponse = $response;
        $this->code = $response['code'] ?? 500;
        $this->message = $response['message'] ?? 'Unknown error';
        $this->data = $response['data'] ?? null;
    }
    
    /**
     * 是否成功
     */
    public function isSuccess()
    {
        return $this->code == 200;
    }
    
    /**
     * 获取响应码
     */
    public function getCode()
    {
        return $this->code;
    }
    
    /**
     * 获取响应消息
     */
    public function getMessage()
    {
        return $this->message;
    }
    
    /**
     * 获取响应数据
     */
    public function getData()
    {
        return $this->data;
    }
    
    /**
     * 获取原始响应
     */
    public function getRawResponse()
    {
        return $this->rawResponse;
    }
    
    /**
     * 获取模板列表数据
     */
    public function getTemplateList()
    {
        if (!$this->isSuccess() || !isset($this->data['list'])) {
            return [];
        }
        
        return $this->data['list'];
    }
    
    /**
     * 获取分页信息
     */
    public function getPaginationInfo()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return [
            'total' => $this->data['total'] ?? 0,
            'page' => $this->data['page'] ?? 1,
            'pageSize' => $this->data['pageSize'] ?? 10,
            'totalPages' => $this->data['totalPages'] ?? 1
        ];
    }
    
    /**
     * 获取模板详情
     */
    public function getTemplateDetail()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return $this->data;
    }
    
    /**
     * 获取解析结果
     */
    public function getParseResult()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return [
            'templateId' => $this->data['templateId'] ?? '',
            'templateTitle' => $this->data['templateTitle'] ?? '',
            'textElements' => $this->data['textElements'] ?? [],
            'parameterCandidates' => $this->data['parameterCandidates'] ?? [],
            'summary' => $this->data['summary'] ?? []
        ];
    }
    
    /**
     * 获取参数候选项
     */
    public function getParameterCandidates()
    {
        $parseResult = $this->getParseResult();
        return $parseResult ? $parseResult['parameterCandidates'] : [];
    }
    
    /**
     * 获取预览URL
     */
    public function getPreviewUrl()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return $this->data['previewUrl'] ?? null;
    }
    
    /**
     * 获取生成的图片URL
     */
    public function getImageUrl()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return $this->data['url'] ?? null;
    }
    
    /**
     * 获取批量任务ID
     */
    public function getBatchId()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return $this->data['batchId'] ?? null;
    }
    
    /**
     * 获取批量任务状态
     */
    public function getBatchStatus()
    {
        if (!$this->isSuccess()) {
            return null;
        }
        
        return [
            'batchId' => $this->data['batchId'] ?? '',
            'status' => $this->data['status'] ?? '',
            'progress' => $this->data['progress'] ?? 0,
            'totalItems' => $this->data['totalItems'] ?? 0,
            'completedItems' => $this->data['completedItems'] ?? 0,
            'failedItems' => $this->data['failedItems'] ?? 0,
            'results' => $this->data['results'] ?? []
        ];
    }
    
    /**
     * 转换为数组
     */
    public function toArray()
    {
        return [
            'code' => $this->code,
            'message' => $this->message,
            'data' => $this->data,
            'success' => $this->isSuccess()
        ];
    }
    
    /**
     * 转换为JSON
     */
    public function toJson()
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 抛出异常（如果响应失败）
     */
    public function throwIfFailed($customMessage = null)
    {
        if (!$this->isSuccess()) {
            $message = $customMessage ?: $this->message;
            throw new \Exception($message, $this->code);
        }
        
        return $this;
    }
    
    /**
     * 获取错误信息
     */
    public function getError()
    {
        if ($this->isSuccess()) {
            return null;
        }
        
        return [
            'code' => $this->code,
            'message' => $this->message,
            'data' => $this->data
        ];
    }
    
    /**
     * 魔术方法：转换为字符串
     */
    public function __toString()
    {
        return $this->toJson();
    }
}
