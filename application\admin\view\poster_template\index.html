{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>模板管理</span>
        <div class="layui-btn-group fr">
            <button class="layui-btn layui-btn-sm" onclick="testConnection()">
                <i class="layui-icon layui-icon-wifi"></i> 测试连接
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="refreshCache()">
                <i class="layui-icon layui-icon-refresh-1"></i> 刷新缓存
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <div class="layui-form layui-form-pane search-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">关键词</label>
                    <div class="layui-input-inline">
                        <input type="text" name="keyword" placeholder="模板标题/ID" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">分类</label>
                    <div class="layui-input-inline">
                        <select name="category">
                            <option value="">全部分类</option>
                            <option value="poster">海报</option>
                            <option value="banner">横幅</option>
                            <option value="card">卡片</option>
                            <option value="social">社交媒体</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="templateTable" lay-filter="templateTable"></table>
    </div>
</div>

<!-- 模板详情弹窗 -->
<div id="templateDetailModal" style="display: none; padding: 20px;">
    <div id="templateDetailContent">
        <!-- 详情内容将通过AJAX加载 -->
    </div>
</div>

<!-- 解析模板弹窗 -->
<div id="parseTemplateModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="parseForm">
        <input type="hidden" name="template_id" id="parseTemplateId">
        <div class="layui-form-item">
            <label class="layui-form-label">模板ID</label>
            <div class="layui-input-block">
                <input type="text" id="parseTemplateIdShow" readonly class="layui-input layui-disabled">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">配置名称</label>
            <div class="layui-input-block">
                <input type="text" name="config_name" required lay-verify="required" placeholder="请输入配置名称" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">配置描述</label>
            <div class="layui-input-block">
                <textarea name="config_description" placeholder="请输入配置描述" class="layui-textarea"></textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="parseSubmit">开始解析</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>

<script type="text/html" id="templateTableToolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i> 刷新
        </button>
    </div>
</script>

<script type="text/html" id="templateTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看详情</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="parse">解析模板</a>
</script>

<script>
layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;

    // 渲染表格
    var tableIns = table.render({
        elem: '#templateTable',
        url: '{:url("poster_template/index")}',
        toolbar: '#templateTableToolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {field: 'id', title: '模板ID', width: 120, fixed: 'left'},
            {field: 'title', title: '模板标题', width: 200},
            {field: 'description', title: '描述', width: 250},
            {field: 'category', title: '分类', width: 100},
            {field: 'tags', title: '标签', width: 150, templet: function(d){
                if(d.tags && d.tags.length > 0) {
                    return d.tags.map(tag => '<span class="layui-badge layui-badge-rim">' + tag + '</span>').join(' ');
                }
                return '-';
            }},
            {field: 'width', title: '宽度', width: 80},
            {field: 'height', title: '高度', width: 80},
            {field: 'createdAt', title: '创建时间', width: 160},
            {title: '操作', width: 180, toolbar: '#templateTableBar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 搜索
    form.on('submit(search)', function(data){
        tableIns.reload({
            where: data.field,
            page: {curr: 1}
        });
        return false;
    });

    // 头工具栏事件
    table.on('toolbar(templateTable)', function(obj){
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(templateTable)', function(obj){
        var data = obj.data;
        switch(obj.event){
            case 'detail':
                showTemplateDetail(data.id);
                break;
            case 'parse':
                showParseModal(data.id, data.title);
                break;
        }
    });

    // 显示模板详情
    function showTemplateDetail(templateId) {
        layer.load();
        $.get('{:url("poster_template/detail")}', {template_id: templateId}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                var content = '<div class="template-detail">';
                content += '<h3>' + res.data.title + '</h3>';
                content += '<p><strong>ID:</strong> ' + res.data.id + '</p>';
                content += '<p><strong>描述:</strong> ' + (res.data.description || '-') + '</p>';
                content += '<p><strong>分类:</strong> ' + (res.data.category || '-') + '</p>';
                content += '<p><strong>尺寸:</strong> ' + res.data.width + ' x ' + res.data.height + '</p>';
                if(res.data.thumbnail) {
                    content += '<p><strong>缩略图:</strong></p>';
                    content += '<img src="' + res.data.thumbnail + '" style="max-width: 300px; max-height: 200px;">';
                }
                content += '</div>';
                
                layer.open({
                    type: 1,
                    title: '模板详情',
                    content: content,
                    area: ['600px', '500px'],
                    shadeClose: true
                });
            } else {
                layer.msg(res.msg || '获取详情失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
    }

    // 显示解析模板弹窗
    function showParseModal(templateId, templateTitle) {
        $('#parseTemplateId').val(templateId);
        $('#parseTemplateIdShow').val(templateId + ' - ' + templateTitle);
        
        layer.open({
            type: 1,
            title: '解析模板',
            content: $('#parseTemplateModal'),
            area: ['500px', '400px'],
            btn: false,
            shadeClose: false
        });
    }

    // 解析表单提交
    form.on('submit(parseSubmit)', function(data){
        layer.load();
        $.post('{:url("poster_template/parse")}', data.field, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                layer.closeAll();
                layer.msg('解析成功', {icon: 1});
                // 可以跳转到配置管理页面
                setTimeout(function(){
                    location.href = '{:url("poster_config/index")}';
                }, 1500);
            } else {
                layer.msg(res.msg || '解析失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
        return false;
    });

    // 测试连接
    window.testConnection = function() {
        layer.load();
        $.post('{:url("poster_template/testConnection")}', {}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                layer.msg('连接测试成功', {icon: 1});
            } else {
                layer.msg(res.msg || '连接测试失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
    };

    // 刷新缓存
    window.refreshCache = function() {
        layer.confirm('确定要刷新缓存吗？', function(index){
            layer.load();
            $.post('{:url("poster_template/refreshCache")}', {}, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('缓存刷新成功', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '缓存刷新失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
            layer.close(index);
        });
    };
});
</script>

<style>
.template-detail img {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    margin-top: 10px;
}
.search-form {
    margin-bottom: 15px;
}
.search-form .layui-form-item {
    margin-bottom: 0;
}
</style>
</div>
