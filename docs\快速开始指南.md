# 动态参数模板系统 - 快速开始指南

本指南将帮助您在5分钟内快速启动和测试动态参数模板系统。

## 🚀 一键启动（推荐）

### 1. 环境检查

```bash
# 检查PHP版本（需要 >= 7.0）
php -v

# 检查MySQL服务
mysql --version
systemctl status mysql  # Linux
brew services list | grep mysql  # macOS

# 检查Composer
composer --version
```

### 2. 快速部署

```bash
# 1. 进入项目目录
cd /path/to/likeshop-server

# 2. 复制环境配置
cp .env.example .env

# 3. 编辑数据库配置（必须）
vim .env
# 修改以下配置：
# DB_HOST=127.0.0.1
# DB_NAME=likeshop
# DB_USER=root
# DB_PASS=your_password

# 4. 一键部署
chmod +x deploy.sh
./deploy.sh dev install
```

### 3. 验证安装

```bash
# 运行基础测试
cd test
phpunit --testsuite=Unit

# 检查数据库表
mysql -u root -p likeshop -e "SHOW TABLES LIKE 'ls_poster_%';"
```

## 🧪 快速测试

### 1. API健康检查

```bash
# 测试外部API
curl -X GET "http://localhost/api/external/health" \
  -H "Authorization: Bearer test-api-key-for-development"

# 预期响应：
# {
#   "code": 200,
#   "message": "success",
#   "data": {
#     "status": "ok",
#     "timestamp": "2025-01-16T10:00:00+00:00",
#     "database": "connected"
#   }
# }
```

### 2. 前端API测试

```bash
# 获取配置列表
curl -X GET "http://localhost/api/poster/configs"

# 获取示例配置
curl -X GET "http://localhost/api/poster/config/test-config-001"

# 预期响应：
# {
#   "code": 200,
#   "message": "success",
#   "data": {
#     "id": "test-config-001",
#     "config_name": "个性化日签配置",
#     "parameters": [...]
#   }
# }
```

### 3. 提交测试数据

```bash
# 提交表单数据
curl -X POST "http://localhost/api/poster/submit" \
  -H "Content-Type: application/json" \
  -H "session-id: test-session-123" \
  -d '{
    "config_id": "test-config-001",
    "parameter_values": {
      "greeting": "你好，快速测试！"
    },
    "is_draft": true
  }'

# 预期响应：
# {
#   "code": 200,
#   "message": "success",
#   "data": {
#     "id": "data_20250116101234_1234",
#     "config_id": "test-config-001",
#     "is_draft": true
#   }
# }
```

## 🎯 核心功能演示

### 1. 后台管理演示

```bash
# 访问后台管理（需要先登录LikeShop后台）
# http://localhost/admin

# 导航路径：
# 后台首页 -> 动态模板 -> 模板管理
# 后台首页 -> 动态模板 -> 参数配置
# 后台首页 -> 动态模板 -> 用户数据
```

### 2. 创建简单测试页面

创建文件 `public/test_poster.html`：

```html
<!DOCTYPE html>
<html>
<head>
    <title>动态参数模板测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 120px; }
        input, select { width: 200px; padding: 5px; }
        button { padding: 10px 20px; margin: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>动态参数模板系统测试</h1>
    
    <div class="form-group">
        <label>配置ID:</label>
        <input type="text" id="configId" value="test-config-001">
        <button onclick="loadConfig()">加载配置</button>
    </div>
    
    <div id="configInfo" class="result" style="display:none;"></div>
    
    <div class="form-group">
        <label>问候语:</label>
        <input type="text" id="greeting" value="你好，世界！">
    </div>
    
    <div>
        <button onclick="submitForm()">提交表单</button>
        <button onclick="generatePreview()" id="previewBtn" disabled>生成预览</button>
        <button onclick="generateImage()" id="imageBtn" disabled>生成图片</button>
    </div>
    
    <div id="result" class="result" style="display:none;"></div>

    <script>
        let currentDataId = null;
        
        async function loadConfig() {
            const configId = document.getElementById('configId').value;
            try {
                const response = await fetch(`/api/poster/config/${configId}`);
                const data = await response.json();
                
                if (data.code === 200) {
                    document.getElementById('configInfo').innerHTML = `
                        <h3>配置信息</h3>
                        <p><strong>名称:</strong> ${data.data.config_name}</p>
                        <p><strong>描述:</strong> ${data.data.config_description}</p>
                        <p><strong>参数数量:</strong> ${data.data.parameters.length}</p>
                    `;
                    document.getElementById('configInfo').style.display = 'block';
                } else {
                    alert('加载配置失败: ' + data.message);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            }
        }
        
        async function submitForm() {
            const configId = document.getElementById('configId').value;
            const greeting = document.getElementById('greeting').value;
            
            try {
                const response = await fetch('/api/poster/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'session-id': 'test-session-' + Date.now()
                    },
                    body: JSON.stringify({
                        config_id: configId,
                        parameter_values: { greeting: greeting },
                        is_draft: true
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    currentDataId = data.data.id;
                    document.getElementById('previewBtn').disabled = false;
                    document.getElementById('imageBtn').disabled = false;
                    showResult('表单提交成功', data.data);
                } else {
                    alert('提交失败: ' + data.message);
                }
            } catch (error) {
                alert('请求失败: ' + error.message);
            }
        }
        
        async function generatePreview() {
            if (!currentDataId) return;
            
            try {
                const response = await fetch(`/api/poster/preview/${currentDataId}`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                showResult('预览生成结果', data);
            } catch (error) {
                alert('预览生成失败: ' + error.message);
            }
        }
        
        async function generateImage() {
            if (!currentDataId) return;
            
            try {
                const response = await fetch('/api/poster/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        data_id: currentDataId,
                        width: 1242,
                        height: 2208,
                        quality: 0.9
                    })
                });
                
                const data = await response.json();
                showResult('图片生成结果', data);
            } catch (error) {
                alert('图片生成失败: ' + error.message);
            }
        }
        
        function showResult(title, data) {
            document.getElementById('result').innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            document.getElementById('result').style.display = 'block';
        }
        
        // 页面加载时自动加载配置
        window.onload = function() {
            loadConfig();
        };
    </script>
</body>
</html>
```

### 3. 访问测试页面

```bash
# 在浏览器中访问
http://localhost/test_poster.html

# 测试步骤：
# 1. 点击"加载配置"查看配置信息
# 2. 修改问候语内容
# 3. 点击"提交表单"
# 4. 点击"生成预览"（Mock模式）
# 5. 点击"生成图片"（Mock模式）
```

## ✅ 验证清单

完成以下检查确保系统正常运行：

- [ ] 部署脚本执行成功
- [ ] 数据库表创建成功
- [ ] 单元测试全部通过
- [ ] API健康检查返回200
- [ ] 可以获取配置列表
- [ ] 可以提交表单数据
- [ ] 测试页面功能正常

## 🔧 常见问题

### 1. 数据库连接失败

```bash
# 检查MySQL服务
systemctl status mysql

# 检查配置
cat .env | grep DB_

# 手动测试连接
mysql -h127.0.0.1 -uroot -p
```

### 2. 权限错误

```bash
# 设置目录权限
chmod -R 755 runtime/
chmod -R 755 uploads/
chmod -R 755 logs/

# 检查Web服务器用户
ps aux | grep apache  # 或 nginx
```

### 3. Composer依赖问题

```bash
# 清理并重新安装
rm -rf vendor/
composer clear-cache
composer install
```

## 📚 下一步

系统启动成功后，您可以：

1. 阅读 [测试验证指南](测试验证指南.md) 进行完整测试
2. 查看 [主项目集成指南](主项目集成指南.md) 了解集成细节
3. 参考 [动态参数模板系统API文档](动态参数模板系统API文档.md) 进行开发

## 🆘 获取帮助

如果遇到问题：

1. 查看 `runtime/log/error.log` 错误日志
2. 运行 `./deploy.sh dev test` 进行诊断
3. 参考完整的测试验证指南
4. 联系技术支持团队

---

**恭喜！** 您已经成功启动了动态参数模板系统！🎉
