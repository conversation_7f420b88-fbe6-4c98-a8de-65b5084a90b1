<?php
/**
 * 测试数据配置
 */

return [
    // 模板配置测试数据
    'template_configs' => [
        [
            'id' => 'test-config-001',
            'template_id' => '2',
            'template_title' => '示例模板 - 日签插画手机海报',
            'config_name' => '个性化日签配置',
            'config_description' => '用于生成个性化日签的模板配置',
            'parameters' => [
                [
                    'id' => 'param-1',
                    'elementUuid' => '98fd9b16db8a',
                    'parameterName' => 'greeting',
                    'parameterLabel' => '个性问候语',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'defaultValue' => '你好,十二月',
                    'validationRules' => ['maxLength' => 20],
                    'displayOrder' => 1,
                    'isEnabled' => true
                ]
            ],
            'created_by' => 'test-admin',
            'status' => 1
        ]
    ],
    
    // 用户数据测试数据
    'user_data' => [
        [
            'id' => 'test-data-001',
            'config_id' => 'test-config-001',
            'user_id' => 'test-user-001',
            'session_id' => null,
            'parameter_values' => [
                'greeting' => '你好,新年快乐'
            ],
            'is_draft' => false,
            'preview_url' => null,
            'generated_image_url' => null
        ],
        [
            'id' => 'test-data-002',
            'config_id' => 'test-config-001',
            'user_id' => null,
            'session_id' => 'test-session-001',
            'parameter_values' => [
                'greeting' => '祝你工作顺利'
            ],
            'is_draft' => true,
            'preview_url' => null,
            'generated_image_url' => null
        ]
    ],
    
    // 生成记录测试数据
    'generation_records' => [
        [
            'id' => 'test-record-001',
            'data_id' => 'test-data-001',
            'image_url' => 'http://localhost:7001/static/generated/test-data-001_1242x2208.jpg',
            'generation_options' => [
                'width' => 1242,
                'height' => 2208,
                'quality' => 0.9
            ],
            'generation_time' => 2.5,
            'file_size' => 256000
        ]
    ],
    
    // API响应测试数据
    'api_responses' => [
        'templates_list' => [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => [
                    [
                        'id' => '2',
                        'title' => '示例模板 - 日签插画手机海报',
                        'description' => '这是一个精美的设计模板',
                        'thumbnail' => 'http://localhost:7001/static/2-cover.jpg',
                        'category' => 'poster',
                        'tags' => ['日签', '插画'],
                        'width' => 1242,
                        'height' => 2208,
                        'createdAt' => '2025-01-16T10:00:00Z',
                        'updatedAt' => '2025-01-16T10:00:00Z'
                    ]
                ],
                'total' => 1,
                'page' => 1,
                'pageSize' => 10,
                'totalPages' => 1
            ]
        ],
        
        'template_parse' => [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'templateId' => '2',
                'templateTitle' => '示例模板 - 日签插画手机海报',
                'parameterCandidates' => [
                    [
                        'elementUuid' => '98fd9b16db8a',
                        'suggestedName' => 'greeting',
                        'suggestedLabel' => '问候语',
                        'suggestedDescription' => '可自定义的文本内容',
                        'suggestedType' => 'text',
                        'originalText' => '你好,十二月',
                        'textCategory' => 'general',
                        'maxLength' => 100,
                        'isRequired' => false
                    ]
                ]
            ]
        ],
        
        'preview_generate' => [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'previewUrl' => 'http://localhost:7001/preview/parameter/test-data-001',
                'success' => true
            ]
        ],
        
        'image_generate' => [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'url' => 'http://localhost:7001/static/generated/test-data-001_1242x2208.jpg',
                'width' => 1242,
                'height' => 2208,
                'fileSize' => 256000,
                'generatedAt' => '2025-01-16T10:00:00Z'
            ]
        ]
    ]
];
