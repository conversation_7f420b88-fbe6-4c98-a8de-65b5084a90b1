<?php
/**
 * 后台管理功能测试
 */

namespace Test\Functional;

use PHPUnit\Framework\TestCase;
use Test\Tools\TestHelper;

class AdminTest extends TestCase
{
    private $baseUrl;
    private $adminSession;
    
    protected function setUp(): void
    {
        TestHelper::initTestEnvironment();
        TestHelper::insertTestData();
        
        $this->baseUrl = 'http://localhost/admin';
        $this->adminSession = $this->loginAsAdmin();
    }
    
    protected function tearDown(): void
    {
        TestHelper::cleanTestData();
    }
    
    /**
     * 模拟管理员登录
     */
    private function loginAsAdmin()
    {
        // 这里应该实现实际的登录逻辑
        // 返回模拟的session数据
        return [
            'admin_id' => 1,
            'admin_name' => 'test_admin',
            'session_id' => 'test_admin_session_' . time()
        ];
    }
    
    /**
     * 测试模板管理功能
     */
    public function testTemplateManagement()
    {
        // 测试模板列表页面
        $response = $this->makeAdminRequest('GET', '/poster_template/index');
        $this->assertEquals(200, $response['http_code']);
        
        // 测试获取模板列表数据
        $response = $this->makeAdminRequest('GET', '/poster_template/index', [
            'page' => 1,
            'limit' => 10
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        $data = $response['data'];
        $this->assertArrayHasKey('lists', $data);
        $this->assertArrayHasKey('count', $data);
        
        // 测试模板详情
        $templateId = '2'; // 使用测试数据中的模板ID
        $response = $this->makeAdminRequest('GET', "/poster_template/detail", [
            'template_id' => $templateId
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        
        // 测试解析模板
        $parseData = [
            'template_id' => $templateId,
            'config_name' => '测试解析配置',
            'config_description' => '这是一个测试解析的配置'
        ];
        
        $response = $this->makeAdminRequest('POST', '/poster_template/parse', $parseData);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试API连接测试
        $response = $this->makeAdminRequest('POST', '/poster_template/testConnection');
        $this->assertEquals(200, $response['http_code']);
        
        // 测试刷新缓存
        $response = $this->makeAdminRequest('POST', '/poster_template/refreshCache');
        $this->assertEquals(200, $response['http_code']);
    }
    
    /**
     * 测试配置管理功能
     */
    public function testConfigManagement()
    {
        // 测试配置列表页面
        $response = $this->makeAdminRequest('GET', '/poster_config/index');
        $this->assertEquals(200, $response['http_code']);
        
        // 测试获取配置列表数据
        $response = $this->makeAdminRequest('GET', '/poster_config/index', [
            'page' => 1,
            'limit' => 10,
            'keyword' => '个性化'
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        $data = $response['data'];
        $this->assertArrayHasKey('lists', $data);
        $this->assertArrayHasKey('count', $data);
        
        // 测试添加配置
        $configData = [
            'template_id' => 'test-template-admin',
            'template_title' => '管理员测试模板',
            'config_name' => '管理员测试配置',
            'config_description' => '这是管理员创建的测试配置',
            'parameters' => [
                [
                    'elementUuid' => 'admin-test-uuid',
                    'parameterName' => 'admin_test_param',
                    'parameterLabel' => '管理员测试参数',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true,
                    'defaultValue' => '管理员默认值'
                ]
            ]
        ];
        
        $response = $this->makeAdminRequest('POST', '/poster_config/add', $configData);
        $this->assertEquals(200, $response['http_code']);
        
        // 获取刚创建的配置ID（这里需要根据实际返回数据调整）
        $configId = 'test-config-admin-001'; // 模拟ID
        
        // 测试配置详情
        $response = $this->makeAdminRequest('GET', "/poster_config/detail", [
            'id' => $configId
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试编辑配置
        $updateData = [
            'id' => $configId,
            'config_name' => '更新后的管理员测试配置',
            'config_description' => '更新后的描述'
        ];
        
        $response = $this->makeAdminRequest('POST', '/poster_config/edit', $updateData);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试切换状态
        $response = $this->makeAdminRequest('POST', '/poster_config/toggleStatus', [
            'id' => $configId,
            'status' => 0
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        $response = $this->makeAdminRequest('POST', '/poster_config/toggleStatus', [
            'id' => $configId,
            'status' => 1
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试复制配置
        $response = $this->makeAdminRequest('POST', '/poster_config/copy', [
            'id' => $configId
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试预览配置
        $response = $this->makeAdminRequest('GET', "/poster_config/preview", [
            'id' => $configId
        ]);
        $this->assertEquals(200, $response['http_code']);
    }
    
    /**
     * 测试用户数据管理功能
     */
    public function testUserDataManagement()
    {
        // 测试用户数据列表页面
        $response = $this->makeAdminRequest('GET', '/poster_data/index');
        $this->assertEquals(200, $response['http_code']);
        
        // 测试获取用户数据列表
        $response = $this->makeAdminRequest('GET', '/poster_data/index', [
            'page' => 1,
            'limit' => 10
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        $data = $response['data'];
        $this->assertArrayHasKey('lists', $data);
        $this->assertArrayHasKey('count', $data);
        
        // 使用测试数据中的用户数据ID
        $dataId = 'test-data-001';
        
        // 测试数据详情
        $response = $this->makeAdminRequest('GET', "/poster_data/detail", [
            'id' => $dataId
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试生成预览
        $response = $this->makeAdminRequest('POST', '/poster_data/generatePreview', [
            'id' => $dataId
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试生成图片
        $response = $this->makeAdminRequest('POST', '/poster_data/generateImage', [
            'id' => $dataId,
            'width' => 1242,
            'height' => 2208,
            'quality' => 0.9
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试批量生成
        $response = $this->makeAdminRequest('POST', '/poster_data/batchGenerate', [
            'ids' => [$dataId],
            'width' => 1242,
            'height' => 2208,
            'quality' => 0.9
        ]);
        $this->assertEquals(200, $response['http_code']);
        
        // 测试导出数据
        $response = $this->makeAdminRequest('GET', '/poster_data/export', [
            'start_date' => date('Y-m-d', strtotime('-7 days')),
            'end_date' => date('Y-m-d')
        ]);
        $this->assertEquals(200, $response['http_code']);
    }
    
    /**
     * 测试权限控制
     */
    public function testPermissionControl()
    {
        // 测试未登录访问
        $response = $this->makeRequest('GET', '/poster_template/index');
        $this->assertNotEquals(200, $response['http_code']); // 应该被重定向或返回错误
        
        // 测试无效session访问
        $response = $this->makeRequest('GET', '/poster_template/index', [], [
            'Cookie' => 'invalid_session=invalid_value'
        ]);
        $this->assertNotEquals(200, $response['http_code']);
    }
    
    /**
     * 测试数据验证
     */
    public function testDataValidation()
    {
        // 测试添加配置时的数据验证
        $invalidConfigData = [
            'template_id' => '', // 空的模板ID
            'config_name' => '', // 空的配置名称
            'parameters' => 'invalid' // 无效的参数格式
        ];
        
        $response = $this->makeAdminRequest('POST', '/poster_config/add', $invalidConfigData);
        $this->assertNotEquals(200, $response['http_code']);
        
        // 测试编辑配置时的数据验证
        $invalidUpdateData = [
            'id' => 'non-existent-id',
            'config_name' => str_repeat('a', 300) // 超长的配置名称
        ];
        
        $response = $this->makeAdminRequest('POST', '/poster_config/edit', $invalidUpdateData);
        $this->assertNotEquals(200, $response['http_code']);
    }
    
    /**
     * 发送管理员请求
     */
    private function makeAdminRequest($method, $path, $data = [], $headers = [])
    {
        $defaultHeaders = [
            'Cookie' => 'admin_session=' . $this->adminSession['session_id'],
            'X-Requested-With' => 'XMLHttpRequest'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        return $this->makeRequest($method, $path, $data, $headers);
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $path, $data = [], $headers = [])
    {
        $url = $this->baseUrl . $path;
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        foreach ($headers as $key => $value) {
            $defaultHeaders[] = "{$key}: {$value}";
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $defaultHeaders,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method
        ]);
        
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
            if ($method === 'GET') {
                $url .= '?' . http_build_query($data);
                curl_setopt($ch, CURLOPT_URL, $url);
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        } elseif ($method === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
            curl_setopt($ch, CURLOPT_URL, $url);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->fail("cURL Error: {$error}");
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'http_code' => $httpCode,
            'data' => $decodedResponse,
            'raw_response' => $response
        ];
    }
}
