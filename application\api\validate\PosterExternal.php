<?php
/**
 * 外部API验证器
 */

namespace app\api\validate;

use think\Validate;

class PosterExternal extends Validate
{
    protected $rule = [
        // 批量获取参数数据
        'dataIds' => 'require|array|max:50',
        'dataIds.*' => 'require|alphaNum|length:1,32',
        
        // 更新参数数据状态
        'previewUrl' => 'url|max:500',
        'generatedImageUrl' => 'url|max:500',
        'isDraft' => 'boolean',
        
        // 统计查询
        'start_date' => 'date',
        'end_date' => 'date',
        
        // 通用参数
        'page' => 'integer|min:1',
        'limit' => 'integer|between:1,100',
    ];
    
    protected $message = [
        'dataIds.require' => 'dataIds参数不能为空',
        'dataIds.array' => 'dataIds必须是数组',
        'dataIds.max' => 'dataIds最多包含50个元素',
        'dataIds.*.require' => 'dataId不能为空',
        'dataIds.*.alphaNum' => 'dataId只能包含字母和数字',
        'dataIds.*.length' => 'dataId长度必须在1-32个字符之间',
        
        'previewUrl.url' => 'previewUrl必须是有效的URL',
        'previewUrl.max' => 'previewUrl长度不能超过500个字符',
        'generatedImageUrl.url' => 'generatedImageUrl必须是有效的URL',
        'generatedImageUrl.max' => 'generatedImageUrl长度不能超过500个字符',
        'isDraft.boolean' => 'isDraft必须是布尔值',
        
        'start_date.date' => 'start_date必须是有效的日期',
        'end_date.date' => 'end_date必须是有效的日期',
        
        'page.integer' => 'page必须是整数',
        'page.min' => 'page最小值为1',
        'limit.integer' => 'limit必须是整数',
        'limit.between' => 'limit必须在1-100之间',
    ];
    
    protected $scene = [
        'batch_parameter_data' => ['dataIds', 'dataIds.*'],
        'update_status' => ['previewUrl', 'generatedImageUrl', 'isDraft'],
        'stats' => ['start_date', 'end_date'],
        'pagination' => ['page', 'limit'],
    ];
    
    /**
     * 验证日期范围
     */
    protected function checkDateRange($value, $rule, $data = [])
    {
        if (isset($data['start_date']) && isset($data['end_date'])) {
            $startDate = strtotime($data['start_date']);
            $endDate = strtotime($data['end_date']);
            
            if ($startDate > $endDate) {
                return 'start_date不能大于end_date';
            }
            
            // 限制查询范围不超过1年
            $maxRange = 365 * 24 * 3600; // 1年
            if (($endDate - $startDate) > $maxRange) {
                return '查询时间范围不能超过1年';
            }
        }
        
        return true;
    }
    
    /**
     * 验证数据ID格式
     */
    protected function checkDataId($value, $rule, $data = [])
    {
        // 检查ID格式：data_YmdHis_xxxx
        if (!preg_match('/^data_\d{14}_\d{4}$/', $value)) {
            return 'dataId格式不正确';
        }
        
        return true;
    }
    
    /**
     * 验证配置ID格式
     */
    protected function checkConfigId($value, $rule, $data = [])
    {
        // 检查ID格式：config_YmdHis_xxxx
        if (!preg_match('/^config_\d{14}_\d{4}$/', $value)) {
            return 'configId格式不正确';
        }
        
        return true;
    }
    
    /**
     * 验证URL是否为HTTPS（生产环境）
     */
    protected function checkSecureUrl($value, $rule, $data = [])
    {
        if (app()->isDebug()) {
            return true; // 开发环境允许HTTP
        }
        
        if (!empty($value) && !str_starts_with($value, 'https://')) {
            return '生产环境必须使用HTTPS URL';
        }
        
        return true;
    }
    
    /**
     * 验证批量操作数量限制
     */
    protected function checkBatchLimit($value, $rule, $data = [])
    {
        $maxBatchSize = config('poster.batch.max_items', 50);
        
        if (is_array($value) && count($value) > $maxBatchSize) {
            return "批量操作最多支持{$maxBatchSize}个项目";
        }
        
        return true;
    }
}
