# 动态参数模板系统测试目录

本目录包含动态参数模板系统的所有测试相关文件。

## 目录结构

```
test/
├── README.md                    # 测试说明文档
├── config/                      # 测试配置文件
│   ├── database.php            # 测试数据库配置
│   ├── poster_api.php          # 迅排设计API配置
│   └── test_data.php           # 测试数据配置
├── data/                       # 测试数据
│   ├── templates/              # 模板测试数据
│   ├── parameters/             # 参数测试数据
│   └── fixtures/               # 测试夹具
├── unit/                       # 单元测试
│   ├── ApiClientTest.php       # API客户端测试
│   ├── ModelTest.php           # 模型测试
│   └── LogicTest.php           # 逻辑层测试
├── integration/                # 集成测试
│   ├── PosterApiTest.php       # 迅排设计API集成测试
│   ├── DatabaseTest.php        # 数据库集成测试
│   └── WorkflowTest.php        # 工作流集成测试
├── functional/                 # 功能测试
│   ├── AdminTest.php           # 后台管理功能测试
│   ├── UserFormTest.php        # 用户表单功能测试
│   └── ApiTest.php             # 外部API功能测试
├── performance/                # 性能测试
│   ├── LoadTest.php            # 负载测试
│   └── StressTest.php          # 压力测试
└── tools/                      # 测试工具
    ├── TestHelper.php          # 测试辅助类
    ├── MockServer.php          # 模拟服务器
    └── DataGenerator.php       # 测试数据生成器
```

## 测试环境要求

- PHP >= 7.0
- ThinkPHP 5.1
- PHPUnit
- MySQL 测试数据库
- 迅排设计服务（可使用Mock）

## 运行测试

### 单元测试
```bash
cd test
phpunit unit/
```

### 集成测试
```bash
cd test
phpunit integration/
```

### 功能测试
```bash
cd test
phpunit functional/
```

### 所有测试
```bash
cd test
phpunit
```

## 测试数据

测试数据存放在 `data/` 目录中，包括：
- 模板数据样例
- 参数配置样例
- 用户数据样例
- API响应样例

## 注意事项

1. 测试前请确保测试数据库已创建
2. 配置正确的迅排设计API地址
3. 测试数据会在测试完成后自动清理
4. 不要在生产环境运行测试
