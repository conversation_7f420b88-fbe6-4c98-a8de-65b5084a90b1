-- 动态参数模板系统数据库表创建脚本
-- 创建时间: 2025-01-16
-- 版本: 1.0

-- 模板参数配置表
CREATE TABLE IF NOT EXISTS `ls_poster_template_configs` (
  `id` VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
  `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  `template_title` VARCHAR(255) COMMENT '模板标题',
  `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
  `config_description` TEXT COMMENT '配置描述',
  `parameters` JSON NOT NULL COMMENT '参数定义JSON',
  `created_by` VARCHAR(32) COMMENT '创建者ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
  
  INDEX `idx_template_id` (`template_id`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板参数配置表';

-- 用户参数数据表
CREATE TABLE IF NOT EXISTS `ls_poster_user_data` (
  `id` VARCHAR(32) PRIMARY KEY COMMENT '数据ID',
  `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
  `user_id` VARCHAR(32) COMMENT '用户ID',
  `session_id` VARCHAR(64) COMMENT '会话ID（匿名用户）',
  `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
  `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  `preview_url` VARCHAR(500) COMMENT '预览页面URL',
  `generated_image_url` VARCHAR(500) COMMENT '生成的图片URL',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX `idx_config_id` (`config_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_session_id` (`session_id`),
  INDEX `idx_is_draft` (`is_draft`),
  INDEX `idx_created_at` (`created_at`),
  FOREIGN KEY (`config_id`) REFERENCES `ls_poster_template_configs`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参数数据表';

-- 图片生成记录表
CREATE TABLE IF NOT EXISTS `ls_poster_generation_records` (
  `id` VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
  `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  `generation_options` JSON COMMENT '生成选项',
  `generation_time` DECIMAL(10,3) COMMENT '生成耗时（秒）',
  `file_size` INT COMMENT '文件大小（字节）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX `idx_data_id` (`data_id`),
  INDEX `idx_created_at` (`created_at`),
  FOREIGN KEY (`data_id`) REFERENCES `ls_poster_user_data`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片生成记录表';

-- 插入示例数据
INSERT INTO `ls_poster_template_configs` (
  `id`, `template_id`, `template_title`, `config_name`, `config_description`, 
  `parameters`, `created_by`, `status`
) VALUES (
  'config_demo_001',
  '2',
  '示例模板 - 日签插画手机海报',
  '个性化日签配置',
  '用于生成个性化日签的模板配置',
  JSON_OBJECT(
    'parameters', JSON_ARRAY(
      JSON_OBJECT(
        'id', 'param-1',
        'elementUuid', '98fd9b16db8a',
        'parameterName', 'greeting',
        'parameterLabel', '个性问候语',
        'parameterType', 'text',
        'isRequired', true,
        'defaultValue', '你好,十二月',
        'validationRules', JSON_OBJECT('maxLength', 20),
        'displayOrder', 1,
        'isEnabled', true
      )
    )
  ),
  'admin',
  1
);
-- Start 分步执行
-- 创建管理员菜单权限
-- 1.先插入父级菜单并获取其ID
INSERT INTO `ls_dev_auth` (
    `pid`, `type`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`
) VALUES 
(0, 1, '动态模板', 'layui-icon-template-1', '', 100, 0, UNIX_TIMESTAMP());

-- 2.获取上一步插入的ID
SET @parent_id = LAST_INSERT_ID();

-- 3.插入子菜单，使用变量 @parent_id
INSERT INTO `ls_dev_auth` (
    `pid`, `type`, `name`, `icon`, `uri`, `sort`, `disable`, `create_time`
) VALUES
(@parent_id, 1, '模板管理', 'layui-icon-list', 'poster_template/index', 99, 0, UNIX_TIMESTAMP()),
(@parent_id, 1, '参数配置', 'layui-icon-set', 'poster_config/index', 98, 0, UNIX_TIMESTAMP()),
(@parent_id, 1, '用户数据', 'layui-icon-user', 'poster_data/index', 97, 0, UNIX_TIMESTAMP()),
(@parent_id, 1, '生成记录', 'layui-icon-chart', 'poster_record/index', 96, 0, UNIX_TIMESTAMP());

-- End 分步执行

-- 创建索引优化查询性能
CREATE INDEX `idx_poster_config_template_status` ON `ls_poster_template_configs` (`template_id`, `status`);
CREATE INDEX `idx_poster_data_user_draft` ON `ls_poster_user_data` (`user_id`, `is_draft`);
CREATE INDEX `idx_poster_data_session_draft` ON `ls_poster_user_data` (`session_id`, `is_draft`);
CREATE INDEX `idx_poster_record_data_created` ON `ls_poster_generation_records` (`data_id`, `created_at`);
