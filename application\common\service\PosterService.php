<?php
/**
 * 动态参数模板服务类
 */

namespace app\common\service;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;
use think\facade\Log;

class PosterService
{
    private $apiClient;
    
    public function __construct()
    {
        $this->apiClient = PosterApiFactory::getInstance();
    }
    
    /**
     * 获取模板列表
     */
    public function getTemplateList($page = 1, $pageSize = 12, $category = null, $keyword = null)
    {
        try {
            $params = [
                'page' => $page,
                'pageSize' => $pageSize
            ];
            
            if ($category) {
                $params['category'] = $category;
            }
            
            if ($keyword) {
                $params['keyword'] = $keyword;
            }
            
            $response = $this->apiClient->getTemplates($params);
            return new PosterApiResponse($response);
        } catch (\Exception $e) {
            Log::error('获取模板列表失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取模板详情
     */
    public function getTemplateDetail($templateId)
    {
        try {
            $response = $this->apiClient->getTemplate($templateId);
            return new PosterApiResponse($response);
        } catch (\Exception $e) {
            Log::error('获取模板详情失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 解析模板并创建配置
     */
    public function parseTemplateAndCreateConfig($templateId, $configName, $configDescription = '', $createdBy = '')
    {
        try {
            // 1. 解析模板
            $parseResponse = $this->apiClient->parseTemplate($templateId);
            $apiResponse = new PosterApiResponse($parseResponse);
            
            if (!$apiResponse->isSuccess()) {
                throw new \Exception('模板解析失败: ' . $apiResponse->getMessage());
            }
            
            $parseResult = $apiResponse->getParseResult();
            
            // 2. 创建配置记录
            $configData = [
                'template_id' => $templateId,
                'template_title' => $parseResult['templateTitle'],
                'config_name' => $configName,
                'config_description' => $configDescription,
                'parameters' => $parseResult['parameterCandidates'],
                'created_by' => $createdBy
            ];
            
            $config = PosterTemplateConfig::createConfig($configData);
            
            if (!$config) {
                throw new \Exception('创建配置失败');
            }
            
            return [
                'config' => $config,
                'parseResult' => $parseResult
            ];
        } catch (\Exception $e) {
            Log::error('解析模板并创建配置失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 保存用户参数数据
     */
    public function saveUserData($configId, $parameterValues, $userId = null, $sessionId = null, $isDraft = true)
    {
        try {
            // 验证配置是否存在
            $config = PosterTemplateConfig::find($configId);
            if (!$config) {
                throw new \Exception('配置不存在');
            }
            
            // 创建用户数据
            $userData = [
                'config_id' => $configId,
                'user_id' => $userId,
                'session_id' => $sessionId,
                'parameter_values' => $parameterValues,
                'is_draft' => $isDraft
            ];
            
            $userDataModel = PosterUserData::createUserData($userData);
            
            if (!$userDataModel) {
                throw new \Exception('保存用户数据失败');
            }
            
            return $userDataModel;
        } catch (\Exception $e) {
            Log::error('保存用户数据失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成预览
     */
    public function generatePreview($dataId)
    {
        try {
            // 获取用户数据
            $userData = PosterUserData::find($dataId);
            if (!$userData) {
                throw new \Exception('用户数据不存在');
            }
            
            $config = $userData->config;
            if (!$config) {
                throw new \Exception('配置不存在');
            }
            
            // 调用API生成预览
            $response = $this->apiClient->generatePreview($config->template_id, $dataId);
            $apiResponse = new PosterApiResponse($response);
            
            if (!$apiResponse->isSuccess()) {
                throw new \Exception('预览生成失败: ' . $apiResponse->getMessage());
            }
            
            // 更新预览URL
            $previewUrl = $apiResponse->getPreviewUrl();
            if ($previewUrl) {
                $userData->setPreviewUrl($previewUrl);
            }
            
            return $apiResponse;
        } catch (\Exception $e) {
            Log::error('生成预览失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 生成图片
     */
    public function generateImage($dataId, $width = 1242, $height = 2208, $quality = 0.9)
    {
        try {
            // 获取用户数据
            $userData = PosterUserData::find($dataId);
            if (!$userData) {
                throw new \Exception('用户数据不存在');
            }
            
            // 生成图片
            $params = [
                'parameterDataId' => $dataId,
                'width' => $width,
                'height' => $height,
                'quality' => $quality
            ];
            
            $startTime = microtime(true);
            $response = $this->apiClient->generateImage($params);
            $endTime = microtime(true);
            $generationTime = round($endTime - $startTime, 3);
            
            $apiResponse = new PosterApiResponse($response);
            
            if (!$apiResponse->isSuccess()) {
                throw new \Exception('图片生成失败: ' . $apiResponse->getMessage());
            }
            
            $imageUrl = $apiResponse->getImageUrl();
            
            // 更新用户数据
            if ($imageUrl) {
                $userData->setGeneratedImageUrl($imageUrl);
                $userData->publish(); // 设置为非草稿状态
            }
            
            // 记录生成历史
            $recordData = [
                'data_id' => $dataId,
                'image_url' => $imageUrl,
                'generation_options' => $params,
                'generation_time' => $generationTime,
                'file_size' => $apiResponse->getData()['fileSize'] ?? 0
            ];
            
            PosterGenerationRecord::createRecord($recordData);
            
            return $apiResponse;
        } catch (\Exception $e) {
            Log::error('生成图片失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 批量生成图片
     */
    public function batchGenerateImages($dataIds, $outputOptions = [])
    {
        try {
            $defaultOptions = [
                'width' => 1242,
                'height' => 2208,
                'quality' => 0.9,
                'type' => 'file'
            ];
            
            $options = array_merge($defaultOptions, $outputOptions);
            
            $response = $this->apiClient->batchGenerate($dataIds, $options);
            return new PosterApiResponse($response);
        } catch (\Exception $e) {
            Log::error('批量生成图片失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 查询批量任务状态
     */
    public function getBatchStatus($batchId)
    {
        try {
            $response = $this->apiClient->getBatchStatus($batchId);
            return new PosterApiResponse($response);
        } catch (\Exception $e) {
            Log::error('查询批量任务状态失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取用户的历史数据
     */
    public function getUserHistory($userId = null, $sessionId = null, $limit = 20)
    {
        try {
            if ($userId) {
                return PosterUserData::getByUserId($userId, false)->limit($limit);
            } elseif ($sessionId) {
                return PosterUserData::getBySessionId($sessionId, false)->limit($limit);
            } else {
                throw new \Exception('用户ID或会话ID不能为空');
            }
        } catch (\Exception $e) {
            Log::error('获取用户历史数据失败: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取生成统计信息
     */
    public function getGenerationStats($startDate = null, $endDate = null)
    {
        try {
            return PosterGenerationRecord::getGenerationStats($startDate, $endDate);
        } catch (\Exception $e) {
            Log::error('获取生成统计信息失败: ' . $e->getMessage());
            throw $e;
        }
    }
}
