<?php
/**
 * 用户表单功能测试
 */

namespace Test\Functional;

use PHPUnit\Framework\TestCase;
use Test\Tools\TestHelper;

class UserFormTest extends TestCase
{
    private $baseUrl;
    private $sessionId;
    
    protected function setUp(): void
    {
        TestHelper::initTestEnvironment();
        TestHelper::insertTestData();
        
        $this->baseUrl = 'http://localhost/api/poster';
        $this->sessionId = 'test_session_' . time();
    }
    
    protected function tearDown(): void
    {
        TestHelper::cleanTestData();
    }
    
    /**
     * 测试获取表单配置
     */
    public function testGetFormConfig()
    {
        $configId = 'test-config-001';
        $response = $this->makeApiRequest('GET', "/config/{$configId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($configId, $data['id']);
        $this->assertArrayHasKey('config_name', $data);
        $this->assertArrayHasKey('parameters', $data);
        $this->assertIsArray($data['parameters']);
        
        // 验证参数结构
        foreach ($data['parameters'] as $param) {
            $this->assertArrayHasKey('parameterName', $param);
            $this->assertArrayHasKey('parameterLabel', $param);
            $this->assertArrayHasKey('parameterType', $param);
            $this->assertArrayHasKey('isRequired', $param);
            $this->assertArrayNotHasKey('elementUuid', $param); // 不应该暴露给前端
        }
    }
    
    /**
     * 测试获取不存在的配置
     */
    public function testGetNonExistentConfig()
    {
        $response = $this->makeApiRequest('GET', '/config/non-existent-config');
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertNotEquals(200, $response['data']['code']);
        $this->assertStringContains('不存在', $response['data']['msg']);
    }
    
    /**
     * 测试提交表单数据
     */
    public function testSubmitForm()
    {
        $configId = 'test-config-001';
        $formData = [
            'config_id' => $configId,
            'parameter_values' => [
                'greeting' => '你好，功能测试！'
            ],
            'is_draft' => true
        ];
        
        $response = $this->makeApiRequest('POST', '/submit', $formData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('id', $data);
        $this->assertEquals($configId, $data['config_id']);
        $this->assertTrue($data['is_draft']);
        
        return $data['id']; // 返回数据ID供后续测试使用
    }
    
    /**
     * 测试提交无效数据
     */
    public function testSubmitInvalidForm()
    {
        // 测试缺少必填参数
        $invalidData = [
            'config_id' => 'test-config-001',
            'parameter_values' => [], // 缺少必填的greeting参数
            'is_draft' => true
        ];
        
        $response = $this->makeApiRequest('POST', '/submit', $invalidData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertNotEquals(200, $response['data']['code']);
        
        // 测试无效的配置ID
        $invalidConfigData = [
            'config_id' => 'invalid-config-id',
            'parameter_values' => [
                'greeting' => '测试'
            ],
            'is_draft' => true
        ];
        
        $response = $this->makeApiRequest('POST', '/submit', $invalidConfigData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertNotEquals(200, $response['data']['code']);
    }
    
    /**
     * 测试更新草稿
     * @depends testSubmitForm
     */
    public function testUpdateDraft()
    {
        // 先创建一个草稿
        $dataId = $this->testSubmitForm();
        
        $updateData = [
            'data_id' => $dataId,
            'parameter_values' => [
                'greeting' => '更新后的问候语'
            ]
        ];
        
        $response = $this->makeApiRequest('PUT', '/draft', $updateData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($dataId, $data['id']);
        $this->assertArrayHasKey('updated_at', $data);
    }
    
    /**
     * 测试发布草稿
     * @depends testSubmitForm
     */
    public function testPublishDraft()
    {
        // 先创建一个草稿
        $dataId = $this->testSubmitForm();
        
        $response = $this->makeApiRequest('POST', "/publish/{$dataId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertEquals($dataId, $data['id']);
        $this->assertFalse($data['is_draft']);
    }
    
    /**
     * 测试生成预览
     * @depends testSubmitForm
     */
    public function testGeneratePreview()
    {
        // 先创建一个数据
        $dataId = $this->testSubmitForm();
        
        $response = $this->makeApiRequest('POST', "/preview/{$dataId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('preview_url', $data);
        $this->assertTrue($data['success']);
    }
    
    /**
     * 测试生成图片
     * @depends testSubmitForm
     */
    public function testGenerateImage()
    {
        // 先创建一个数据
        $dataId = $this->testSubmitForm();
        
        $generateData = [
            'data_id' => $dataId,
            'width' => 1242,
            'height' => 2208,
            'quality' => 0.9
        ];
        
        $response = $this->makeApiRequest('POST', '/generate', $generateData);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('image_url', $data);
        $this->assertEquals(1242, $data['width']);
        $this->assertEquals(2208, $data['height']);
        $this->assertTrue($data['success']);
    }
    
    /**
     * 测试获取用户历史数据
     */
    public function testGetUserHistory()
    {
        // 先创建一些数据
        $this->testSubmitForm();
        $this->testSubmitForm();
        
        $response = $this->makeApiRequest('GET', '/history', [
            'page' => 1,
            'limit' => 10
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('count', $data);
        $this->assertArrayHasKey('lists', $data);
        $this->assertGreaterThan(0, $data['count']);
        $this->assertIsArray($data['lists']);
        
        // 验证历史数据结构
        if (!empty($data['lists'])) {
            $firstItem = $data['lists'][0];
            $this->assertArrayHasKey('id', $firstItem);
            $this->assertArrayHasKey('config_name', $firstItem);
            $this->assertArrayHasKey('parameter_values', $firstItem);
            $this->assertArrayHasKey('is_draft', $firstItem);
        }
    }
    
    /**
     * 测试删除用户数据
     * @depends testSubmitForm
     */
    public function testDeleteUserData()
    {
        // 先创建一个数据
        $dataId = $this->testSubmitForm();
        
        $response = $this->makeApiRequest('DELETE', "/data/{$dataId}");
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        // 验证数据已删除 - 再次获取历史数据应该不包含已删除的数据
        $historyResponse = $this->makeApiRequest('GET', '/history');
        $historyData = $historyResponse['data']['data'];
        
        $deletedDataExists = false;
        foreach ($historyData['lists'] as $item) {
            if ($item['id'] === $dataId) {
                $deletedDataExists = true;
                break;
            }
        }
        
        $this->assertFalse($deletedDataExists);
    }
    
    /**
     * 测试获取配置列表
     */
    public function testGetConfigList()
    {
        $response = $this->makeApiRequest('GET', '/configs', [
            'page' => 1,
            'limit' => 10,
            'keyword' => '个性化'
        ]);
        
        $this->assertEquals(200, $response['http_code']);
        $this->assertEquals(200, $response['data']['code']);
        
        $data = $response['data']['data'];
        $this->assertArrayHasKey('count', $data);
        $this->assertArrayHasKey('lists', $data);
        $this->assertIsArray($data['lists']);
        
        // 验证配置列表结构
        if (!empty($data['lists'])) {
            $firstConfig = $data['lists'][0];
            $this->assertArrayHasKey('id', $firstConfig);
            $this->assertArrayHasKey('config_name', $firstConfig);
            $this->assertArrayHasKey('template_title', $firstConfig);
        }
    }
    
    /**
     * 测试参数验证
     */
    public function testParameterValidation()
    {
        $configId = 'test-config-001';
        
        // 测试邮箱验证（如果配置中有邮箱参数）
        $emailData = [
            'config_id' => $configId,
            'parameter_values' => [
                'email' => 'invalid-email' // 无效邮箱
            ],
            'is_draft' => true
        ];
        
        // 这个测试需要根据实际的参数配置来调整
        // 如果测试配置中没有邮箱参数，这个测试可能会失败
        
        // 测试必填参数验证
        $missingRequiredData = [
            'config_id' => $configId,
            'parameter_values' => [], // 缺少必填参数
            'is_draft' => true
        ];
        
        $response = $this->makeApiRequest('POST', '/submit', $missingRequiredData);
        $this->assertEquals(200, $response['http_code']);
        $this->assertNotEquals(200, $response['data']['code']);
    }
    
    /**
     * 测试会话管理
     */
    public function testSessionManagement()
    {
        // 使用不同的session ID创建数据
        $sessionId1 = 'session_1_' . time();
        $sessionId2 = 'session_2_' . time();
        
        // 为session1创建数据
        $response1 = $this->makeApiRequest('POST', '/submit', [
            'config_id' => 'test-config-001',
            'parameter_values' => ['greeting' => 'Session 1 数据'],
            'is_draft' => true
        ], ['session-id' => $sessionId1]);
        
        $this->assertEquals(200, $response1['http_code']);
        
        // 为session2创建数据
        $response2 = $this->makeApiRequest('POST', '/submit', [
            'config_id' => 'test-config-001',
            'parameter_values' => ['greeting' => 'Session 2 数据'],
            'is_draft' => true
        ], ['session-id' => $sessionId2]);
        
        $this->assertEquals(200, $response2['http_code']);
        
        // 验证session1只能看到自己的数据
        $history1 = $this->makeApiRequest('GET', '/history', [], ['session-id' => $sessionId1]);
        $this->assertEquals(200, $history1['http_code']);
        
        // 验证session2只能看到自己的数据
        $history2 = $this->makeApiRequest('GET', '/history', [], ['session-id' => $sessionId2]);
        $this->assertEquals(200, $history2['http_code']);
        
        // 数据应该是隔离的
        $data1 = $history1['data']['data']['lists'];
        $data2 = $history2['data']['data']['lists'];
        
        $this->assertNotEmpty($data1);
        $this->assertNotEmpty($data2);
        $this->assertNotEquals($data1[0]['id'], $data2[0]['id']);
    }
    
    /**
     * 发送API请求
     */
    private function makeApiRequest($method, $path, $data = [], $headers = [])
    {
        $url = $this->baseUrl . $path;
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json',
            'session-id: ' . $this->sessionId
        ];
        
        foreach ($headers as $key => $value) {
            $defaultHeaders[] = "{$key}: {$value}";
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $defaultHeaders,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method
        ]);
        
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } elseif ($method === 'GET' && !empty($data)) {
            $url .= '?' . http_build_query($data);
            curl_setopt($ch, CURLOPT_URL, $url);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->fail("cURL Error: {$error}");
        }
        
        $decodedResponse = json_decode($response, true);
        
        return [
            'http_code' => $httpCode,
            'data' => $decodedResponse,
            'raw_response' => $response
        ];
    }
}
