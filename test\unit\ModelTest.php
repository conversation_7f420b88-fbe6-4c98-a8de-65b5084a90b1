<?php
/**
 * 模型单元测试
 */

namespace Test\Unit;

use PHPUnit\Framework\TestCase;
use Test\Tools\TestHelper;
use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\model\PosterGenerationRecord;

class ModelTest extends TestCase
{
    protected function setUp(): void
    {
        TestHelper::initTestEnvironment();
    }
    
    protected function tearDown(): void
    {
        TestHelper::cleanTestData();
    }
    
    /**
     * 测试模板配置模型
     */
    public function testPosterTemplateConfigModel()
    {
        // 测试创建配置
        $configData = [
            'template_id' => 'test-template-001',
            'template_title' => '测试模板',
            'config_name' => '测试配置',
            'config_description' => '这是一个测试配置',
            'parameters' => [
                [
                    'elementUuid' => 'test-uuid-001',
                    'parameterName' => 'test_param',
                    'parameterLabel' => '测试参数',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true,
                    'defaultValue' => '默认值'
                ]
            ],
            'created_by' => 'test-admin'
        ];
        
        $config = PosterTemplateConfig::createConfig($configData);
        
        $this->assertInstanceOf(PosterTemplateConfig::class, $config);
        $this->assertEquals($configData['template_id'], $config->template_id);
        $this->assertEquals($configData['config_name'], $config->config_name);
        $this->assertEquals(PosterTemplateConfig::STATUS_ENABLED, $config->status);
        $this->assertIsArray($config->parameters);
        
        // 测试参数统计
        $stats = $config->getParameterStats();
        $this->assertIsArray($stats);
        $this->assertEquals(1, $stats['total']);
        $this->assertEquals(1, $stats['enabled']);
        $this->assertEquals(1, $stats['required']);
        
        // 测试更新配置
        $updateData = [
            'config_name' => '更新后的配置名称',
            'config_description' => '更新后的描述'
        ];
        
        $result = $config->updateConfig($updateData);
        $this->assertTrue($result);
        $this->assertEquals($updateData['config_name'], $config->config_name);
        
        // 测试获取启用的配置
        $enabledConfigs = PosterTemplateConfig::getEnabledConfigs();
        $this->assertGreaterThan(0, count($enabledConfigs));
        
        // 测试根据模板ID获取配置
        $configByTemplate = PosterTemplateConfig::getByTemplateId($configData['template_id']);
        $this->assertInstanceOf(PosterTemplateConfig::class, $configByTemplate);
        $this->assertEquals($config->id, $configByTemplate->id);
    }
    
    /**
     * 测试用户数据模型
     */
    public function testPosterUserDataModel()
    {
        // 先创建一个配置
        $configData = [
            'template_id' => 'test-template-002',
            'template_title' => '测试模板2',
            'config_name' => '测试配置2',
            'parameters' => [
                [
                    'elementUuid' => 'test-uuid-002',
                    'parameterName' => 'greeting',
                    'parameterLabel' => '问候语',
                    'parameterType' => 'text',
                    'isRequired' => true,
                    'isEnabled' => true
                ]
            ],
            'created_by' => 'test-admin'
        ];
        
        $config = PosterTemplateConfig::createConfig($configData);
        
        // 测试创建用户数据
        $userData = [
            'config_id' => $config->id,
            'user_id' => 'test-user-001',
            'parameter_values' => [
                'greeting' => '你好，世界！'
            ],
            'is_draft' => true
        ];
        
        $userDataModel = PosterUserData::createUserData($userData);
        
        $this->assertInstanceOf(PosterUserData::class, $userDataModel);
        $this->assertEquals($userData['config_id'], $userDataModel->config_id);
        $this->assertEquals($userData['user_id'], $userDataModel->user_id);
        $this->assertTrue($userDataModel->is_draft);
        $this->assertIsArray($userDataModel->parameter_values);
        
        // 测试关联查询
        $configRelation = $userDataModel->config;
        $this->assertInstanceOf(PosterTemplateConfig::class, $configRelation);
        $this->assertEquals($config->id, $configRelation->id);
        
        // 测试更新用户数据
        $updateData = [
            'parameter_values' => [
                'greeting' => '更新后的问候语'
            ],
            'is_draft' => false
        ];
        
        $result = $userDataModel->updateUserData($updateData);
        $this->assertTrue($result);
        $this->assertFalse($userDataModel->is_draft);
        
        // 测试设置预览URL
        $previewUrl = 'http://test.com/preview/123';
        $result = $userDataModel->setPreviewUrl($previewUrl);
        $this->assertTrue($result);
        $this->assertEquals($previewUrl, $userDataModel->preview_url);
        
        // 测试设置生成图片URL
        $imageUrl = 'http://test.com/image/123.jpg';
        $result = $userDataModel->setGeneratedImageUrl($imageUrl);
        $this->assertTrue($result);
        $this->assertEquals($imageUrl, $userDataModel->generated_image_url);
        
        // 测试发布
        $userDataModel->is_draft = true;
        $result = $userDataModel->publish();
        $this->assertTrue($result);
        $this->assertFalse($userDataModel->is_draft);
        
        // 测试根据用户ID获取数据
        $userDataList = PosterUserData::getByUserId('test-user-001');
        $this->assertGreaterThan(0, count($userDataList));
        
        // 测试获取完整数据
        $fullData = $userDataModel->getFullData();
        $this->assertIsArray($fullData);
        $this->assertArrayHasKey('templateId', $fullData);
        $this->assertArrayHasKey('parameterValues', $fullData);
    }
    
    /**
     * 测试生成记录模型
     */
    public function testPosterGenerationRecordModel()
    {
        // 先创建配置和用户数据
        $config = PosterTemplateConfig::createConfig([
            'template_id' => 'test-template-003',
            'config_name' => '测试配置3',
            'parameters' => [],
            'created_by' => 'test-admin'
        ]);
        
        $userData = PosterUserData::createUserData([
            'config_id' => $config->id,
            'user_id' => 'test-user-002',
            'parameter_values' => ['test' => 'value'],
            'is_draft' => false
        ]);
        
        // 测试创建生成记录
        $recordData = [
            'data_id' => $userData->id,
            'image_url' => 'http://test.com/generated/image.jpg',
            'generation_options' => [
                'width' => 1242,
                'height' => 2208,
                'quality' => 0.9
            ],
            'generation_time' => 2.5,
            'file_size' => 256000
        ];
        
        $record = PosterGenerationRecord::createRecord($recordData);
        
        $this->assertInstanceOf(PosterGenerationRecord::class, $record);
        $this->assertEquals($recordData['data_id'], $record->data_id);
        $this->assertEquals($recordData['image_url'], $record->image_url);
        $this->assertEquals($recordData['generation_time'], $record->generation_time);
        $this->assertEquals($recordData['file_size'], $record->file_size);
        $this->assertIsArray($record->generation_options);
        
        // 测试关联查询
        $userDataRelation = $record->userData;
        $this->assertInstanceOf(PosterUserData::class, $userDataRelation);
        $this->assertEquals($userData->id, $userDataRelation->id);
        
        // 测试根据数据ID获取记录
        $records = PosterGenerationRecord::getByDataId($userData->id);
        $this->assertGreaterThan(0, count($records));
        
        // 测试获取最新记录
        $latestRecord = PosterGenerationRecord::getLatestByDataId($userData->id);
        $this->assertInstanceOf(PosterGenerationRecord::class, $latestRecord);
        $this->assertEquals($record->id, $latestRecord->id);
        
        // 测试数组转换
        $recordArray = $record->toArray();
        $this->assertIsArray($recordArray);
        $this->assertArrayHasKey('file_size_formatted', $recordArray);
        $this->assertArrayHasKey('generation_time_formatted', $recordArray);
    }
    
    /**
     * 测试模型验证
     */
    public function testModelValidation()
    {
        // 测试参数验证
        $config = new PosterTemplateConfig();
        
        // 有效参数
        $validParameters = [
            [
                'elementUuid' => 'test-uuid',
                'parameterName' => 'test_param',
                'parameterLabel' => '测试参数',
                'parameterType' => 'text',
                'isRequired' => true,
                'isEnabled' => true
            ]
        ];
        
        $this->assertTrue($config->validateParameters($validParameters));
        
        // 无效参数（缺少必需字段）
        $invalidParameters = [
            [
                'elementUuid' => 'test-uuid',
                'parameterName' => 'test_param'
                // 缺少其他必需字段
            ]
        ];
        
        $this->assertFalse($config->validateParameters($invalidParameters));
        
        // 测试用户数据验证
        $userData = new PosterUserData();
        $config = PosterTemplateConfig::createConfig([
            'template_id' => 'test-template-004',
            'config_name' => '验证测试配置',
            'parameters' => $validParameters,
            'created_by' => 'test-admin'
        ]);
        
        // 有效参数值
        $validValues = ['test_param' => '测试值'];
        $this->assertTrue($userData->validateParameterValues($validValues, $config));
        
        // 无效参数值（缺少必填参数）
        $invalidValues = [];
        $this->assertFalse($userData->validateParameterValues($invalidValues, $config));
    }
}
