<?php
/**
 * 测试辅助类
 */

namespace Test\Tools;

use think\Db;
use think\Config;

class TestHelper
{
    /**
     * 初始化测试环境
     */
    public static function initTestEnvironment()
    {
        // 加载测试配置
        $testConfig = include __DIR__ . '/../config/database.php';
        Config::set('database', $testConfig);
        
        // 创建测试数据库表
        self::createTestTables();
        
        // 清理测试数据
        self::cleanTestData();
    }
    
    /**
     * 创建测试数据库表
     */
    public static function createTestTables()
    {
        $sql = [
            // 模板参数配置表
            "CREATE TABLE IF NOT EXISTS `ls_test_poster_template_configs` (
                `id` VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
                `template_id` VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
                `template_title` VARCHAR(255) COMMENT '模板标题',
                `config_name` VARCHAR(255) NOT NULL COMMENT '配置名称',
                `config_description` TEXT COMMENT '配置描述',
                `parameters` JSON NOT NULL COMMENT '参数定义JSON',
                `created_by` VARCHAR(32) COMMENT '创建者ID',
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `status` TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
                INDEX `idx_template_id` (`template_id`),
                INDEX `idx_created_by` (`created_by`),
                INDEX `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板参数配置表'",
            
            // 用户参数数据表
            "CREATE TABLE IF NOT EXISTS `ls_test_poster_user_data` (
                `id` VARCHAR(32) PRIMARY KEY COMMENT '数据ID',
                `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
                `user_id` VARCHAR(32) COMMENT '用户ID',
                `session_id` VARCHAR(64) COMMENT '会话ID（匿名用户）',
                `parameter_values` JSON NOT NULL COMMENT '用户填写的参数值',
                `is_draft` BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
                `preview_url` VARCHAR(500) COMMENT '预览页面URL',
                `generated_image_url` VARCHAR(500) COMMENT '生成的图片URL',
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX `idx_config_id` (`config_id`),
                INDEX `idx_user_id` (`user_id`),
                INDEX `idx_session_id` (`session_id`),
                INDEX `idx_is_draft` (`is_draft`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参数数据表'",
            
            // 图片生成记录表
            "CREATE TABLE IF NOT EXISTS `ls_test_poster_generation_records` (
                `id` VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
                `data_id` VARCHAR(32) NOT NULL COMMENT '参数数据ID',
                `image_url` VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
                `generation_options` JSON COMMENT '生成选项',
                `generation_time` DECIMAL(10,3) COMMENT '生成耗时（秒）',
                `file_size` INT COMMENT '文件大小（字节）',
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX `idx_data_id` (`data_id`),
                INDEX `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片生成记录表'"
        ];
        
        foreach ($sql as $query) {
            try {
                Db::execute($query);
            } catch (\Exception $e) {
                echo "创建表失败: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 清理测试数据
     */
    public static function cleanTestData()
    {
        $tables = [
            'poster_template_configs',
            'poster_user_data', 
            'poster_generation_records'
        ];
        
        foreach ($tables as $table) {
            try {
                Db::name($table)->delete(true);
            } catch (\Exception $e) {
                // 忽略表不存在的错误
            }
        }
    }
    
    /**
     * 插入测试数据
     */
    public static function insertTestData()
    {
        $testData = include __DIR__ . '/../config/test_data.php';
        
        // 插入模板配置数据
        foreach ($testData['template_configs'] as $config) {
            $config['parameters'] = json_encode($config['parameters']);
            Db::name('poster_template_configs')->insert($config);
        }
        
        // 插入用户数据
        foreach ($testData['user_data'] as $userData) {
            $userData['parameter_values'] = json_encode($userData['parameter_values']);
            Db::name('poster_user_data')->insert($userData);
        }
        
        // 插入生成记录
        foreach ($testData['generation_records'] as $record) {
            $record['generation_options'] = json_encode($record['generation_options']);
            Db::name('poster_generation_records')->insert($record);
        }
    }
    
    /**
     * 生成唯一ID
     */
    public static function generateId($prefix = '')
    {
        return $prefix . date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 模拟HTTP响应
     */
    public static function mockHttpResponse($data, $code = 200)
    {
        return [
            'code' => $code,
            'data' => $data,
            'headers' => [
                'Content-Type' => 'application/json'
            ]
        ];
    }
    
    /**
     * 断言数组包含指定键
     */
    public static function assertArrayHasKeys($array, $keys)
    {
        foreach ($keys as $key) {
            if (!array_key_exists($key, $array)) {
                throw new \Exception("Array does not contain key: {$key}");
            }
        }
        return true;
    }
    
    /**
     * 断言JSON格式正确
     */
    public static function assertValidJson($json)
    {
        $decoded = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Invalid JSON: " . json_last_error_msg());
        }
        return $decoded;
    }
}
