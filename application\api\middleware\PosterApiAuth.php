<?php
/**
 * 迅排设计API认证中间件
 */

namespace app\api\middleware;

use think\facade\Env;
use think\facade\Log;
use think\Request;
use think\Response;

class PosterApiAuth
{
    /**
     * 处理请求
     */
    public function handle(Request $request, \Closure $next)
    {
        // 获取API密钥
        $apiKey = $this->getApiKeyFromRequest($request);
        
        // 验证API密钥
        if (!$this->validateApiKey($apiKey)) {
            return $this->unauthorizedResponse('Invalid API key');
        }
        
        // 记录API调用日志
        $this->logApiCall($request, $apiKey);
        
        // 检查请求频率限制
        if (!$this->checkRateLimit($request, $apiKey)) {
            return $this->rateLimitResponse();
        }
        
        return $next($request);
    }
    
    /**
     * 从请求中获取API密钥
     */
    private function getApiKeyFromRequest(Request $request)
    {
        // 从Authorization头获取
        $authorization = $request->header('Authorization');
        if ($authorization && strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }
        
        // 从X-API-Key头获取
        $apiKey = $request->header('X-API-Key');
        if ($apiKey) {
            return $apiKey;
        }
        
        // 从查询参数获取（不推荐，仅用于测试）
        return $request->param('api_key');
    }
    
    /**
     * 验证API密钥
     */
    private function validateApiKey($apiKey)
    {
        if (empty($apiKey)) {
            return false;
        }
        
        // 从环境变量获取有效的API密钥
        $validApiKeys = $this->getValidApiKeys();
        
        return in_array($apiKey, $validApiKeys);
    }
    
    /**
     * 获取有效的API密钥列表
     */
    private function getValidApiKeys()
    {
        $keys = [];
        
        // 主API密钥
        $mainKey = Env::get('poster.api_key');
        if ($mainKey) {
            $keys[] = $mainKey;
        }
        
        // 测试API密钥
        $testKey = Env::get('poster.test_api_key', 'test-api-key-for-development');
        if ($testKey) {
            $keys[] = $testKey;
        }
        
        // 从配置文件获取额外的密钥
        $extraKeys = config('poster.api_keys', []);
        if (is_array($extraKeys)) {
            $keys = array_merge($keys, $extraKeys);
        }
        
        return array_filter($keys);
    }
    
    /**
     * 记录API调用日志
     */
    private function logApiCall(Request $request, $apiKey)
    {
        $logData = [
            'method' => $request->method(),
            'url' => $request->url(true),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'api_key_hash' => substr(md5($apiKey), 0, 8), // 只记录密钥的哈希前8位
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        Log::info('PosterAPI External Call', $logData);
    }
    
    /**
     * 检查请求频率限制
     */
    private function checkRateLimit(Request $request, $apiKey)
    {
        $rateLimit = Env::get('poster.rate_limit', 1000); // 每小时1000次请求
        $rateLimitWindow = Env::get('poster.rate_limit_window', 3600); // 1小时窗口
        
        if ($rateLimit <= 0) {
            return true; // 禁用频率限制
        }
        
        $cacheKey = 'poster_api_rate_limit_' . md5($apiKey);
        $currentCount = cache($cacheKey) ?: 0;
        
        if ($currentCount >= $rateLimit) {
            return false;
        }
        
        // 增加计数
        cache($cacheKey, $currentCount + 1, $rateLimitWindow);
        
        return true;
    }
    
    /**
     * 返回未授权响应
     */
    private function unauthorizedResponse($message = 'Unauthorized')
    {
        $data = [
            'code' => 401,
            'message' => $message,
            'error' => [
                'reason' => 'Invalid or missing API key',
                'timestamp' => date('c')
            ]
        ];
        
        return Response::create($data, 'json', 401)
            ->header('Content-Type', 'application/json');
    }
    
    /**
     * 返回频率限制响应
     */
    private function rateLimitResponse()
    {
        $data = [
            'code' => 429,
            'message' => 'Too Many Requests',
            'error' => [
                'reason' => 'Rate limit exceeded',
                'timestamp' => date('c')
            ]
        ];
        
        return Response::create($data, 'json', 429)
            ->header('Content-Type', 'application/json')
            ->header('Retry-After', '3600');
    }
    
    /**
     * 获取当前API密钥的使用统计
     */
    public static function getApiKeyStats($apiKey)
    {
        $cacheKey = 'poster_api_rate_limit_' . md5($apiKey);
        $currentCount = cache($cacheKey) ?: 0;
        $rateLimit = Env::get('poster.rate_limit', 1000);
        
        return [
            'current_requests' => $currentCount,
            'rate_limit' => $rateLimit,
            'remaining_requests' => max(0, $rateLimit - $currentCount),
            'reset_time' => time() + 3600
        ];
    }
}
