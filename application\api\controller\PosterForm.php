<?php
/**
 * 动态表单API控制器
 */

namespace app\api\controller;

use app\api\logic\PosterFormLogic;
use app\common\model\PosterTemplateConfig;

class PosterForm extends ApiBase
{
    public $like_not_need_login = ['getConfig', 'submitForm', 'getPreview', 'generateImage'];
    
    /**
     * 获取表单配置
     */
    public function getConfig()
    {
        $configId = $this->request->param('config_id');
        
        if (empty($configId)) {
            return $this->_error('配置ID不能为空');
        }
        
        try {
            $config = PosterFormLogic::getFormConfig($configId);
            if ($config) {
                return $this->_success('获取成功', $config);
            } else {
                return $this->_error('配置不存在或已禁用');
            }
        } catch (\Exception $e) {
            return $this->_error('获取失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 提交表单数据
     */
    public function submitForm()
    {
        $post = $this->request->post();
        
        $result = $this->validate($post, [
            'config_id' => 'require',
            'parameter_values' => 'require|array',
            'is_draft' => 'boolean'
        ]);
        
        if ($result !== true) {
            return $this->_error($result);
        }
        
        try {
            // 获取用户信息
            $userId = $this->user_info['user_id'] ?? null;
            $sessionId = $this->request->header('session-id') ?? session_id();
            
            $result = PosterFormLogic::submitForm(
                $post['config_id'],
                $post['parameter_values'],
                $userId,
                $sessionId,
                $post['is_draft'] ?? true
            );
            
            if ($result) {
                return $this->_success('提交成功', $result);
            } else {
                return $this->_error('提交失败');
            }
        } catch (\Exception $e) {
            return $this->_error('提交失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取预览
     */
    public function getPreview()
    {
        $dataId = $this->request->param('data_id');
        
        if (empty($dataId)) {
            return $this->_error('数据ID不能为空');
        }
        
        try {
            $result = PosterFormLogic::generatePreview($dataId);
            if ($result) {
                return $this->_success('预览生成成功', $result);
            } else {
                return $this->_error('预览生成失败');
            }
        } catch (\Exception $e) {
            return $this->_error('预览生成失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成图片
     */
    public function generateImage()
    {
        $post = $this->request->post();
        
        $result = $this->validate($post, [
            'data_id' => 'require',
            'width' => 'integer|min:100|max:4000',
            'height' => 'integer|min:100|max:4000',
            'quality' => 'float|between:0.1,1.0'
        ]);
        
        if ($result !== true) {
            return $this->_error($result);
        }
        
        try {
            $result = PosterFormLogic::generateImage(
                $post['data_id'],
                $post['width'] ?? 1242,
                $post['height'] ?? 2208,
                $post['quality'] ?? 0.9
            );
            
            if ($result) {
                return $this->_success('图片生成成功', $result);
            } else {
                return $this->_error('图片生成失败');
            }
        } catch (\Exception $e) {
            return $this->_error('图片生成失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取用户历史数据
     */
    public function getUserHistory()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        
        try {
            $userId = $this->user_info['user_id'] ?? null;
            $sessionId = $this->request->header('session-id') ?? session_id();
            
            $result = PosterFormLogic::getUserHistory($userId, $sessionId, $page, $limit);
            return $this->_success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->_error('获取失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 删除用户数据
     */
    public function deleteUserData()
    {
        $dataId = $this->request->param('data_id');
        
        if (empty($dataId)) {
            return $this->_error('数据ID不能为空');
        }
        
        try {
            $userId = $this->user_info['user_id'] ?? null;
            $sessionId = $this->request->header('session-id') ?? session_id();
            
            $result = PosterFormLogic::deleteUserData($dataId, $userId, $sessionId);
            if ($result) {
                return $this->_success('删除成功');
            } else {
                return $this->_error('删除失败');
            }
        } catch (\Exception $e) {
            return $this->_error('删除失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取可用配置列表
     */
    public function getConfigList()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        $keyword = $this->request->param('keyword', '');
        
        try {
            $result = PosterFormLogic::getConfigList($page, $limit, $keyword);
            return $this->_success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->_error('获取失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新草稿数据
     */
    public function updateDraft()
    {
        $post = $this->request->post();
        
        $result = $this->validate($post, [
            'data_id' => 'require',
            'parameter_values' => 'require|array'
        ]);
        
        if ($result !== true) {
            return $this->_error($result);
        }
        
        try {
            $userId = $this->user_info['user_id'] ?? null;
            $sessionId = $this->request->header('session-id') ?? session_id();
            
            $result = PosterFormLogic::updateDraft(
                $post['data_id'],
                $post['parameter_values'],
                $userId,
                $sessionId
            );
            
            if ($result) {
                return $this->_success('更新成功', $result);
            } else {
                return $this->_error('更新失败');
            }
        } catch (\Exception $e) {
            return $this->_error('更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发布草稿
     */
    public function publishDraft()
    {
        $dataId = $this->request->param('data_id');
        
        if (empty($dataId)) {
            return $this->_error('数据ID不能为空');
        }
        
        try {
            $userId = $this->user_info['user_id'] ?? null;
            $sessionId = $this->request->header('session-id') ?? session_id();
            
            $result = PosterFormLogic::publishDraft($dataId, $userId, $sessionId);
            if ($result) {
                return $this->_success('发布成功', $result);
            } else {
                return $this->_error('发布失败');
            }
        } catch (\Exception $e) {
            return $this->_error('发布失败: ' . $e->getMessage());
        }
    }
}
