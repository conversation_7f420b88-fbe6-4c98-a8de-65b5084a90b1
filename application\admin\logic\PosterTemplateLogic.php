<?php
/**
 * 动态模板逻辑层
 */

namespace app\admin\logic;

use app\common\service\PosterService;
use app\common\service\PosterApiFactory;

class PosterTemplateLogic
{
    /**
     * 获取模板列表
     */
    public static function getTemplateList($get)
    {
        try {
            $page = $get['page'] ?? 1;
            $limit = $get['limit'] ?? 10;
            $category = $get['category'] ?? null;
            $keyword = $get['keyword'] ?? null;
            
            $posterService = new PosterService();
            $response = $posterService->getTemplateList($page, $limit, $category, $keyword);
            
            if ($response->isSuccess()) {
                $data = $response->getData();
                return [
                    'count' => $data['total'] ?? 0,
                    'lists' => $data['list'] ?? [],
                    'pagination' => $response->getPaginationInfo()
                ];
            } else {
                return [
                    'count' => 0,
                    'lists' => [],
                    'error' => $response->getMessage()
                ];
            }
        } catch (\Exception $e) {
            return [
                'count' => 0,
                'lists' => [],
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取模板详情
     */
    public static function getTemplateDetail($templateId)
    {
        try {
            $posterService = new PosterService();
            $response = $posterService->getTemplateDetail($templateId);
            
            if ($response->isSuccess()) {
                return $response->getData();
            } else {
                return null;
            }
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 解析模板
     */
    public static function parseTemplate($templateId, $configName, $configDescription = '', $createdBy = '')
    {
        try {
            $posterService = new PosterService();
            return $posterService->parseTemplateAndCreateConfig(
                $templateId,
                $configName,
                $configDescription,
                $createdBy
            );
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 测试API连接
     */
    public static function testApiConnection()
    {
        try {
            return PosterApiFactory::testConnection();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取API状态
     */
    public static function getApiStatus()
    {
        try {
            return PosterApiFactory::getApiStatus();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 清理缓存
     */
    public static function clearCache()
    {
        try {
            $client = PosterApiFactory::getInstance();
            $client->clearCache();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取模板分类
     */
    public static function getTemplateCategories()
    {
        // 这里可以从API获取分类，或者返回预定义的分类
        return [
            ['value' => 'poster', 'label' => '海报'],
            ['value' => 'banner', 'label' => '横幅'],
            ['value' => 'card', 'label' => '卡片'],
            ['value' => 'social', 'label' => '社交媒体'],
            ['value' => 'business', 'label' => '商务'],
            ['value' => 'event', 'label' => '活动'],
        ];
    }
    
    /**
     * 获取模板统计信息
     */
    public static function getTemplateStats()
    {
        try {
            $posterService = new PosterService();
            
            // 获取总数
            $totalResponse = $posterService->getTemplateList(1, 1);
            $total = 0;
            if ($totalResponse->isSuccess()) {
                $paginationInfo = $totalResponse->getPaginationInfo();
                $total = $paginationInfo['total'] ?? 0;
            }
            
            // 获取各分类统计
            $categories = self::getTemplateCategories();
            $categoryStats = [];
            
            foreach ($categories as $category) {
                $categoryResponse = $posterService->getTemplateList(1, 1, $category['value']);
                $count = 0;
                if ($categoryResponse->isSuccess()) {
                    $paginationInfo = $categoryResponse->getPaginationInfo();
                    $count = $paginationInfo['total'] ?? 0;
                }
                $categoryStats[] = [
                    'category' => $category['label'],
                    'count' => $count
                ];
            }
            
            return [
                'total' => $total,
                'categories' => $categoryStats,
                'api_status' => self::getApiStatus()
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'categories' => [],
                'api_status' => ['success' => false, 'message' => $e->getMessage()]
            ];
        }
    }
}
