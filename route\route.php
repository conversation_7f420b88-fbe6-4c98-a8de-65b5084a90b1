<?php

use think\facade\Config;
use think\facade\Route;

//手机h5页面路由
Route::rule('mobile/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('h5', 'is_open', 1);
    if (!$isOpen) {
        return '';
    }
    Config::set('app_trace', false);
    return view(app()->getRootPath() . 'public/mobile/index.html');
})->pattern(['any' => '\w+']);

//手机h5页面路由
Route::rule('pc/:any', function () {
    $isOpen = \app\common\server\ConfigServer::get('pc', 'is_open', 1);
    if (!$isOpen) {
        return '';
    }
    Config::set('app_trace', false);
    return view(app()->getRootPath() . 'public/pc/index.html');
})->pattern(['any' => '\w+']);

//定时任务
Route::rule('crontab', function () {
    think\Console::call('crontab');
});

// 动态参数模板系统外部API路由
Route::group('api/external', function () {
    // 健康检查
    Route::get('health', 'api/PosterExternal/health');

    // 获取参数数据
    Route::get('parameter-data/:dataId', 'api/PosterExternal/getParameterData');

    // 获取参数配置
    Route::get('parameter-config/:configId', 'api/PosterExternal/getParameterConfig');

    // 批量获取参数数据
    Route::post('parameter-data/batch', 'api/PosterExternal/getBatchParameterData');

    // 更新参数数据状态
    Route::put('parameter-data/:dataId/status', 'api/PosterExternal/updateParameterDataStatus');

    // 获取统计信息
    Route::get('stats', 'api/PosterExternal/getStats');
})->middleware('app\api\middleware\PosterApiAuth');

// 动态参数模板系统前端API路由
Route::group('api/poster', function () {
    // 获取表单配置
    Route::get('config/:config_id', 'api/PosterForm/getConfig');

    // 获取可用配置列表
    Route::get('configs', 'api/PosterForm/getConfigList');

    // 提交表单数据
    Route::post('submit', 'api/PosterForm/submitForm');

    // 更新草稿数据
    Route::put('draft', 'api/PosterForm/updateDraft');

    // 发布草稿
    Route::post('publish/:data_id', 'api/PosterForm/publishDraft');

    // 生成预览
    Route::post('preview/:data_id', 'api/PosterForm/getPreview');

    // 生成图片
    Route::post('generate', 'api/PosterForm/generateImage');

    // 获取用户历史数据
    Route::get('history', 'api/PosterForm/getUserHistory');

    // 删除用户数据
    Route::delete('data/:data_id', 'api/PosterForm/deleteUserData');
});