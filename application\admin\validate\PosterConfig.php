<?php
/**
 * 参数配置验证器
 */

namespace app\admin\validate;

use think\Validate;

class PosterConfig extends Validate
{
    protected $rule = [
        'id' => 'require',
        'template_id' => 'require|alphaNum|max:32',
        'template_title' => 'max:255',
        'config_name' => 'require|max:255|unique:poster_template_configs,config_name^id',
        'config_description' => 'max:1000',
        'parameters' => 'require|array|checkParameters',
        'status' => 'in:0,1'
    ];
    
    protected $message = [
        'id.require' => 'ID不能为空',
        'template_id.require' => '模板ID不能为空',
        'template_id.alphaNum' => '模板ID格式不正确',
        'template_id.max' => '模板ID长度不能超过32个字符',
        'template_title.max' => '模板标题长度不能超过255个字符',
        'config_name.require' => '配置名称不能为空',
        'config_name.max' => '配置名称长度不能超过255个字符',
        'config_name.unique' => '配置名称已存在',
        'config_description.max' => '配置描述长度不能超过1000个字符',
        'parameters.require' => '参数配置不能为空',
        'parameters.array' => '参数配置必须是数组',
        'status.in' => '状态值不正确'
    ];
    
    protected $scene = [
        'add' => ['template_id', 'template_title', 'config_name', 'config_description', 'parameters'],
        'edit' => ['id', 'template_title', 'config_name', 'config_description', 'parameters'],
        'status' => ['id', 'status']
    ];
    
    /**
     * 验证参数配置格式
     */
    protected function checkParameters($value, $rule, $data = [])
    {
        if (!is_array($value)) {
            return '参数配置必须是数组';
        }
        
        if (empty($value)) {
            return '参数配置不能为空';
        }
        
        $requiredFields = [
            'elementUuid', 'parameterName', 'parameterLabel', 
            'parameterType', 'isRequired', 'isEnabled'
        ];
        
        $parameterNames = [];
        
        foreach ($value as $index => $param) {
            // 检查必需字段
            foreach ($requiredFields as $field) {
                if (!isset($param[$field])) {
                    return "参数配置第" . ($index + 1) . "项缺少字段: {$field}";
                }
            }
            
            // 验证参数名称唯一性
            $paramName = $param['parameterName'];
            if (in_array($paramName, $parameterNames)) {
                return "参数名称 '{$paramName}' 重复";
            }
            $parameterNames[] = $paramName;
            
            // 验证参数名称格式
            if (!preg_match('/^[a-zA-Z][a-zA-Z0-9_]*$/', $paramName)) {
                return "参数名称 '{$paramName}' 格式不正确，只能包含字母、数字和下划线，且必须以字母开头";
            }
            
            // 验证参数类型
            $allowedTypes = ['text', 'textarea', 'email', 'phone', 'url', 'number', 'select', 'radio', 'checkbox'];
            if (!in_array($param['parameterType'], $allowedTypes)) {
                return "参数类型 '{$param['parameterType']}' 不支持";
            }
            
            // 验证布尔值字段
            if (!is_bool($param['isRequired'])) {
                return "参数配置第" . ($index + 1) . "项的 isRequired 必须是布尔值";
            }
            
            if (!is_bool($param['isEnabled'])) {
                return "参数配置第" . ($index + 1) . "项的 isEnabled 必须是布尔值";
            }
            
            // 验证验证规则
            if (isset($param['validationRules']) && !empty($param['validationRules'])) {
                if (!is_array($param['validationRules'])) {
                    return "参数配置第" . ($index + 1) . "项的验证规则必须是数组";
                }
                
                // 验证具体的验证规则
                $validationResult = $this->validateValidationRules($param['validationRules'], $param['parameterType']);
                if ($validationResult !== true) {
                    return "参数配置第" . ($index + 1) . "项的验证规则错误: {$validationResult}";
                }
            }
            
            // 验证选项（对于select、radio、checkbox类型）
            if (in_array($param['parameterType'], ['select', 'radio', 'checkbox'])) {
                if (!isset($param['options']) || !is_array($param['options']) || empty($param['options'])) {
                    return "参数配置第" . ($index + 1) . "项缺少选项配置";
                }
                
                foreach ($param['options'] as $optionIndex => $option) {
                    if (!isset($option['value']) || !isset($option['label'])) {
                        return "参数配置第" . ($index + 1) . "项的选项第" . ($optionIndex + 1) . "项缺少value或label";
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证验证规则
     */
    private function validateValidationRules($rules, $parameterType)
    {
        $allowedRules = [
            'text' => ['minLength', 'maxLength', 'pattern'],
            'textarea' => ['minLength', 'maxLength'],
            'email' => ['maxLength'],
            'phone' => ['pattern'],
            'url' => ['maxLength'],
            'number' => ['min', 'max', 'step']
        ];
        
        $typeRules = $allowedRules[$parameterType] ?? [];
        
        foreach ($rules as $ruleName => $ruleValue) {
            if (!in_array($ruleName, $typeRules)) {
                return "参数类型 '{$parameterType}' 不支持验证规则 '{$ruleName}'";
            }
            
            // 验证具体规则值
            switch ($ruleName) {
                case 'minLength':
                case 'maxLength':
                    if (!is_int($ruleValue) || $ruleValue < 0) {
                        return "验证规则 '{$ruleName}' 的值必须是非负整数";
                    }
                    break;
                    
                case 'min':
                case 'max':
                case 'step':
                    if (!is_numeric($ruleValue)) {
                        return "验证规则 '{$ruleName}' 的值必须是数字";
                    }
                    break;
                    
                case 'pattern':
                    if (!is_string($ruleValue) || empty($ruleValue)) {
                        return "验证规则 '{$ruleName}' 的值必须是非空字符串";
                    }
                    // 验证正则表达式是否有效
                    if (@preg_match($ruleValue, '') === false) {
                        return "验证规则 '{$ruleName}' 的正则表达式无效";
                    }
                    break;
            }
        }
        
        // 验证规则之间的逻辑关系
        if (isset($rules['minLength']) && isset($rules['maxLength'])) {
            if ($rules['minLength'] > $rules['maxLength']) {
                return "最小长度不能大于最大长度";
            }
        }
        
        if (isset($rules['min']) && isset($rules['max'])) {
            if ($rules['min'] > $rules['max']) {
                return "最小值不能大于最大值";
            }
        }
        
        return true;
    }
}
