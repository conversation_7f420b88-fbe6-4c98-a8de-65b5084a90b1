<?php
/**
 * 动态模板管理控制器
 */

namespace app\admin\controller;

use app\admin\logic\PosterTemplateLogic;
use app\common\service\PosterService;

class PosterTemplate extends AdminBase
{
    /**
     * 模板列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            try {
                $result = PosterTemplateLogic::getTemplateList($get);
                return $this->_success('', $result);
            } catch (\Exception $e) {
                return $this->_error('获取模板列表失败: ' . $e->getMessage());
            }
        }

        return $this->fetch();
    }
    
    /**
     * 模板详情
     */
    public function detail()
    {
        $templateId = $this->request->param('template_id');
        
        if ($this->request->isAjax()) {
            try {
                $posterService = new PosterService();
                $response = $posterService->getTemplateDetail($templateId);
                
                if ($response->isSuccess()) {
                    return $this->_success('获取成功', $response->getData());
                } else {
                    return $this->_error($response->getMessage());
                }
            } catch (\Exception $e) {
                return $this->_error('获取模板详情失败: ' . $e->getMessage());
            }
        }
        
        $this->assign('template_id', $templateId);
        return $this->fetch();
    }
    
    /**
     * 解析模板
     */
    public function parse()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'template_id' => 'require',
                'config_name' => 'require|max:255',
                'config_description' => 'max:1000'
            ]);
            
            if ($result !== true) {
                return $this->_error($result);
            }
            
            try {
                $posterService = new PosterService();
                $result = $posterService->parseTemplateAndCreateConfig(
                    $post['template_id'],
                    $post['config_name'],
                    $post['config_description'] ?? '',
                    $this->admin_info['id']
                );
                
                return $this->_success('解析成功', $result);
            } catch (\Exception $e) {
                return $this->_error('解析失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 刷新模板缓存
     */
    public function refreshCache()
    {
        if ($this->request->isAjax()) {
            try {
                $posterService = new PosterService();
                $posterService->clearCache();
                return $this->_success('缓存刷新成功');
            } catch (\Exception $e) {
                return $this->_error('缓存刷新失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 测试API连接
     */
    public function testConnection()
    {
        if ($this->request->isAjax()) {
            try {
                $posterService = new PosterService();
                $result = $posterService->testApiConnection();
                
                if ($result['success']) {
                    return $this->_success('连接测试成功', $result);
                } else {
                    return $this->_error('连接测试失败: ' . $result['message']);
                }
            } catch (\Exception $e) {
                return $this->_error('连接测试失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
}
