{layout name="layout1" /}
<div class="layui-fluid">
    <div class="layui-card">
    <div class="layui-card-header">
        <span>用户数据管理</span>
        <div class="layui-btn-group fr">
            <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="exportData()">
                <i class="layui-icon layui-icon-export"></i> 导出数据
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索区域 -->
        <div class="layui-form layui-form-pane search-form">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">用户ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="user_id" placeholder="用户ID" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">配置ID</label>
                    <div class="layui-input-inline">
                        <input type="text" name="config_id" placeholder="配置ID" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-inline">
                        <select name="is_draft">
                            <option value="">全部状态</option>
                            <option value="1">草稿</option>
                            <option value="0">已发布</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">时间范围</label>
                    <div class="layui-input-inline">
                        <input type="text" name="date_range" placeholder="选择日期范围" class="layui-input" id="dateRange">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" lay-submit lay-filter="search">
                        <i class="layui-icon layui-icon-search"></i> 搜索
                    </button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<script type="text/html" id="dataTableToolbar">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="refresh">
            <i class="layui-icon layui-icon-refresh"></i> 刷新
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchDel">
            <i class="layui-icon layui-icon-delete"></i> 批量删除
        </button>
    </div>
</script>

<script type="text/html" id="dataTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="preview">预览</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="generate">生成</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
</script>

<script type="text/html" id="statusTpl">
    {{# if(d.is_draft == 1) { }}
        <span class="layui-badge layui-badge-rim">草稿</span>
    {{# } else { }}
        <span class="layui-badge">已发布</span>
    {{# } }}
</script>

<script type="text/html" id="imageTpl">
    {{# if(d.generated_image_url) { }}
        <a href="{{d.generated_image_url}}" target="_blank" class="layui-btn layui-btn-xs layui-btn-normal">查看图片</a>
    {{# } else { }}
        <span class="layui-text-muted">未生成</span>
    {{# } }}
</script>

<script>
layui.use(['table', 'form', 'layer', 'laydate'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;

    // 日期范围选择器
    laydate.render({
        elem: '#dateRange',
        type: 'date',
        range: true
    });

    // 渲染表格
    var tableIns = table.render({
        elem: '#dataTable',
        url: '{:url("poster_data/index")}',
        toolbar: '#dataTableToolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            {type: 'checkbox', fixed: 'left'},
            {field: 'id', title: '数据ID', width: 120, fixed: 'left'},
            {field: 'config_name', title: '配置名称', width: 150},
            {field: 'user_id', title: '用户ID', width: 120},
            {field: 'session_id', title: '会话ID', width: 120},
            {field: 'is_draft', title: '状态', width: 100, templet: '#statusTpl', align: 'center'},
            {field: 'preview_url', title: '预览', width: 100, templet: function(d){
                return d.preview_url ? '<a href="' + d.preview_url + '" target="_blank" class="layui-btn layui-btn-xs">查看</a>' : '<span class="layui-text-muted">未生成</span>';
            }},
            {field: 'generated_image_url', title: '图片', width: 100, templet: '#imageTpl'},
            {field: 'created_at', title: '创建时间', width: 160},
            {field: 'updated_at', title: '更新时间', width: 160},
            {title: '操作', width: 200, toolbar: '#dataTableBar', fixed: 'right'}
        ]],
        page: true,
        height: 'full-220'
    });

    // 搜索
    form.on('submit(search)', function(data){
        var searchData = data.field;
        
        // 处理日期范围
        if(searchData.date_range) {
            var dates = searchData.date_range.split(' - ');
            searchData.start_date = dates[0];
            searchData.end_date = dates[1];
            delete searchData.date_range;
        }
        
        tableIns.reload({
            where: searchData,
            page: {curr: 1}
        });
        return false;
    });

    // 头工具栏事件
    table.on('toolbar(dataTable)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                tableIns.reload();
                break;
            case 'batchDel':
                var data = checkStatus.data;
                if(data.length === 0){
                    layer.msg('请选择要删除的数据');
                    return;
                }
                batchDelete(data);
                break;
        }
    });

    // 行工具栏事件
    table.on('tool(dataTable)', function(obj){
        var data = obj.data;
        switch(obj.event){
            case 'detail':
                showDataDetail(data.id);
                break;
            case 'preview':
                generatePreview(data.id);
                break;
            case 'generate':
                generateImage(data.id);
                break;
            case 'del':
                deleteData(data.id);
                break;
        }
    });

    // 显示数据详情
    function showDataDetail(dataId) {
        layer.load();
        $.get('{:url("poster_data/detail")}', {id: dataId}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                var data = res.data;
                var content = '<div class="data-detail">';
                content += '<h3>数据详情</h3>';
                content += '<p><strong>数据ID:</strong> ' + data.id + '</p>';
                content += '<p><strong>配置名称:</strong> ' + data.config_name + '</p>';
                content += '<p><strong>用户ID:</strong> ' + (data.user_id || '-') + '</p>';
                content += '<p><strong>会话ID:</strong> ' + (data.session_id || '-') + '</p>';
                content += '<p><strong>状态:</strong> ' + (data.is_draft ? '草稿' : '已发布') + '</p>';
                content += '<p><strong>参数值:</strong></p>';
                content += '<pre>' + JSON.stringify(data.parameter_values, null, 2) + '</pre>';
                if(data.preview_url) {
                    content += '<p><strong>预览链接:</strong> <a href="' + data.preview_url + '" target="_blank">查看预览</a></p>';
                }
                if(data.generated_image_url) {
                    content += '<p><strong>生成图片:</strong> <a href="' + data.generated_image_url + '" target="_blank">查看图片</a></p>';
                }
                content += '</div>';
                
                layer.open({
                    type: 1,
                    title: '数据详情',
                    content: content,
                    area: ['700px', '600px'],
                    shadeClose: true
                });
            } else {
                layer.msg(res.msg || '获取详情失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
    }

    // 生成预览
    function generatePreview(dataId) {
        layer.load();
        $.post('{:url("poster_data/generatePreview")}', {id: dataId}, function(res){
            layer.closeAll('loading');
            if(res.code == 1) {
                layer.msg('预览生成成功', {icon: 1});
                if(res.data && res.data.preview_url) {
                    layer.confirm('预览生成成功，是否立即查看？', function(index){
                        window.open(res.data.preview_url);
                        layer.close(index);
                    });
                }
                tableIns.reload();
            } else {
                layer.msg(res.msg || '预览生成失败', {icon: 2});
            }
        }).fail(function(){
            layer.closeAll('loading');
            layer.msg('请求失败', {icon: 2});
        });
    }

    // 生成图片
    function generateImage(dataId) {
        layer.prompt({
            formType: 0,
            title: '生成图片',
            value: '1242x2208',
            tips: '请输入图片尺寸，格式：宽x高'
        }, function(value, index){
            var size = value.split('x');
            if(size.length !== 2) {
                layer.msg('尺寸格式错误', {icon: 2});
                return;
            }
            
            var width = parseInt(size[0]);
            var height = parseInt(size[1]);
            
            if(isNaN(width) || isNaN(height) || width < 100 || height < 100) {
                layer.msg('尺寸必须为大于100的数字', {icon: 2});
                return;
            }
            
            layer.close(index);
            layer.load();
            
            $.post('{:url("poster_data/generateImage")}', {
                id: dataId,
                width: width,
                height: height,
                quality: 0.9
            }, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('图片生成成功', {icon: 1});
                    if(res.data && res.data.image_url) {
                        layer.confirm('图片生成成功，是否立即查看？', function(index){
                            window.open(res.data.image_url);
                            layer.close(index);
                        });
                    }
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '图片生成失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
        });
    }

    // 删除数据
    function deleteData(dataId) {
        layer.confirm('确定要删除这条数据吗？删除后不可恢复！', function(index){
            layer.load();
            $.post('{:url("poster_data/del")}', {id: dataId}, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('删除成功', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '删除失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
            layer.close(index);
        });
    }

    // 批量删除
    function batchDelete(data) {
        var ids = data.map(function(item) {
            return item.id;
        });
        
        layer.confirm('确定要删除选中的 ' + ids.length + ' 条数据吗？删除后不可恢复！', function(index){
            layer.load();
            $.post('{:url("poster_data/batchDel")}', {ids: ids}, function(res){
                layer.closeAll('loading');
                if(res.code == 1) {
                    layer.msg('批量删除成功', {icon: 1});
                    tableIns.reload();
                } else {
                    layer.msg(res.msg || '批量删除失败', {icon: 2});
                }
            }).fail(function(){
                layer.closeAll('loading');
                layer.msg('请求失败', {icon: 2});
            });
            layer.close(index);
        });
    }

    // 导出数据
    window.exportData = function() {
        layer.prompt({
            formType: 0,
            title: '导出数据',
            value: '7',
            tips: '请输入导出最近几天的数据（默认7天）'
        }, function(value, index){
            var days = parseInt(value);
            if(isNaN(days) || days < 1) {
                layer.msg('天数必须为大于0的数字', {icon: 2});
                return;
            }
            
            layer.close(index);
            
            var startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            var endDate = new Date();
            
            var url = '{:url("poster_data/export")}?start_date=' + 
                      startDate.toISOString().split('T')[0] + 
                      '&end_date=' + endDate.toISOString().split('T')[0];
            
            window.open(url);
        });
    };
});
</script>

<style>
.data-detail pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
}
.search-form {
    margin-bottom: 15px;
}
.search-form .layui-form-item {
    margin-bottom: 0;
}
</style>
</div>
