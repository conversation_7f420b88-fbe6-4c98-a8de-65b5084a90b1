<?php
/**
 * 模板参数配置模型
 */

namespace app\common\model;

use think\Model;

class PosterTemplateConfig extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'poster_template_configs';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'string',
        'template_id'       => 'string',
        'template_title'    => 'string',
        'config_name'       => 'string',
        'config_description'=> 'text',
        'parameters'        => 'json',
        'created_by'        => 'string',
        'created_at'        => 'timestamp',
        'updated_at'        => 'timestamp',
        'status'            => 'tinyint',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // JSON字段
    protected $json = ['parameters'];
    
    // 字段类型转换
    protected $type = [
        'parameters' => 'array',
        'status'     => 'integer',
        'created_at' => 'timestamp',
        'updated_at' => 'timestamp',
    ];
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 获取启用的配置列表
     */
    public static function getEnabledConfigs($templateId = null)
    {
        $where = ['status' => self::STATUS_ENABLED];
        if ($templateId) {
            $where['template_id'] = $templateId;
        }
        
        return self::where($where)
            ->order('created_at', 'desc')
            ->select();
    }
    
    /**
     * 根据模板ID获取配置
     */
    public static function getByTemplateId($templateId)
    {
        return self::where('template_id', $templateId)
            ->where('status', self::STATUS_ENABLED)
            ->find();
    }
    
    /**
     * 创建新配置
     */
    public static function createConfig($data)
    {
        $config = new self();
        $config->id = self::generateId();
        $config->template_id = $data['template_id'];
        $config->template_title = $data['template_title'] ?? '';
        $config->config_name = $data['config_name'];
        $config->config_description = $data['config_description'] ?? '';
        $config->parameters = $data['parameters'];
        $config->created_by = $data['created_by'] ?? '';
        $config->status = self::STATUS_ENABLED;
        
        return $config->save() ? $config : false;
    }
    
    /**
     * 更新配置
     */
    public function updateConfig($data)
    {
        $allowedFields = [
            'template_title', 'config_name', 'config_description', 
            'parameters', 'status'
        ];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $this->$field = $data[$field];
            }
        }
        
        return $this->save();
    }
    
    /**
     * 生成唯一ID
     */
    private static function generateId()
    {
        return 'config_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * 验证参数格式
     */
    public function validateParameters($parameters)
    {
        if (!is_array($parameters)) {
            return false;
        }
        
        $requiredFields = [
            'elementUuid', 'parameterName', 'parameterLabel', 
            'parameterType', 'isRequired', 'isEnabled'
        ];
        
        foreach ($parameters as $param) {
            foreach ($requiredFields as $field) {
                if (!isset($param[$field])) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 获取参数统计信息
     */
    public function getParameterStats()
    {
        $parameters = $this->parameters;
        if (!is_array($parameters)) {
            return [];
        }
        
        $stats = [
            'total' => count($parameters),
            'enabled' => 0,
            'required' => 0,
            'types' => []
        ];
        
        foreach ($parameters as $param) {
            if ($param['isEnabled']) {
                $stats['enabled']++;
            }
            if ($param['isRequired']) {
                $stats['required']++;
            }
            
            $type = $param['parameterType'];
            $stats['types'][$type] = ($stats['types'][$type] ?? 0) + 1;
        }
        
        return $stats;
    }
}
