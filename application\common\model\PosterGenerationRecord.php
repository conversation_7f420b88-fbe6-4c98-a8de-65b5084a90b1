<?php
/**
 * 图片生成记录模型
 */

namespace app\common\model;

use think\Model;

class PosterGenerationRecord extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'poster_generation_records';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'                 => 'string',
        'data_id'            => 'string',
        'image_url'          => 'string',
        'generation_options' => 'json',
        'generation_time'    => 'decimal',
        'file_size'          => 'int',
        'created_at'         => 'timestamp',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'created_at';
    protected $updateTime = false;
    
    // JSON字段
    protected $json = ['generation_options'];
    
    // 字段类型转换
    protected $type = [
        'generation_options' => 'array',
        'generation_time'    => 'float',
        'file_size'          => 'integer',
        'created_at'         => 'timestamp',
    ];
    
    /**
     * 关联用户数据
     */
    public function userData()
    {
        return $this->belongsTo(PosterUserData::class, 'data_id', 'id');
    }
    
    /**
     * 创建生成记录
     */
    public static function createRecord($data)
    {
        $record = new self();
        $record->id = self::generateId();
        $record->data_id = $data['data_id'];
        $record->image_url = $data['image_url'];
        $record->generation_options = $data['generation_options'] ?? [];
        $record->generation_time = $data['generation_time'] ?? 0;
        $record->file_size = $data['file_size'] ?? 0;
        
        return $record->save() ? $record : false;
    }
    
    /**
     * 根据数据ID获取记录
     */
    public static function getByDataId($dataId, $limit = null)
    {
        $query = self::where('data_id', $dataId)
            ->order('created_at', 'desc');
            
        if ($limit) {
            $query->limit($limit);
        }
        
        return $query->select();
    }
    
    /**
     * 获取最新的生成记录
     */
    public static function getLatestByDataId($dataId)
    {
        return self::where('data_id', $dataId)
            ->order('created_at', 'desc')
            ->find();
    }
    
    /**
     * 获取生成统计信息
     */
    public static function getGenerationStats($startDate = null, $endDate = null)
    {
        $where = [];
        if ($startDate) {
            $where[] = ['created_at', '>=', $startDate];
        }
        if ($endDate) {
            $where[] = ['created_at', '<=', $endDate];
        }
        
        $records = self::where($where)->select();
        
        $stats = [
            'total_count' => count($records),
            'total_size' => 0,
            'avg_generation_time' => 0,
            'avg_file_size' => 0,
            'size_distribution' => [],
            'time_distribution' => []
        ];
        
        if ($stats['total_count'] > 0) {
            $totalTime = 0;
            $totalSize = 0;
            
            foreach ($records as $record) {
                $totalTime += $record->generation_time;
                $totalSize += $record->file_size;
                
                // 文件大小分布（KB）
                $sizeKB = round($record->file_size / 1024);
                $sizeRange = self::getSizeRange($sizeKB);
                $stats['size_distribution'][$sizeRange] = 
                    ($stats['size_distribution'][$sizeRange] ?? 0) + 1;
                
                // 生成时间分布（秒）
                $timeRange = self::getTimeRange($record->generation_time);
                $stats['time_distribution'][$timeRange] = 
                    ($stats['time_distribution'][$timeRange] ?? 0) + 1;
            }
            
            $stats['avg_generation_time'] = round($totalTime / $stats['total_count'], 2);
            $stats['avg_file_size'] = round($totalSize / $stats['total_count']);
            $stats['total_size'] = $totalSize;
        }
        
        return $stats;
    }
    
    /**
     * 获取文件大小范围
     */
    private static function getSizeRange($sizeKB)
    {
        if ($sizeKB < 100) return '< 100KB';
        if ($sizeKB < 500) return '100-500KB';
        if ($sizeKB < 1024) return '500KB-1MB';
        if ($sizeKB < 2048) return '1-2MB';
        return '> 2MB';
    }
    
    /**
     * 获取生成时间范围
     */
    private static function getTimeRange($time)
    {
        if ($time < 1) return '< 1s';
        if ($time < 3) return '1-3s';
        if ($time < 5) return '3-5s';
        if ($time < 10) return '5-10s';
        return '> 10s';
    }
    
    /**
     * 清理过期记录
     */
    public static function cleanExpiredRecords($days = 30)
    {
        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return self::where('created_at', '<', $expireDate)->delete();
    }
    
    /**
     * 生成唯一ID
     */
    private static function generateId()
    {
        return 'record_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }
    
    /**
     * 格式化输出
     */
    public function toArray()
    {
        $data = parent::toArray();
        
        // 格式化文件大小
        $data['file_size_formatted'] = self::formatFileSize($data['file_size']);
        
        // 格式化生成时间
        $data['generation_time_formatted'] = $data['generation_time'] . 's';
        
        return $data;
    }
    
    /**
     * 格式化文件大小
     */
    private static function formatFileSize($bytes)
    {
        if ($bytes < 1024) return $bytes . 'B';
        if ($bytes < 1048576) return round($bytes / 1024, 1) . 'KB';
        if ($bytes < 1073741824) return round($bytes / 1048576, 1) . 'MB';
        return round($bytes / 1073741824, 1) . 'GB';
    }
}
