<?php
/**
 * 动态表单逻辑层
 */

namespace app\api\logic;

use app\common\model\PosterTemplateConfig;
use app\common\model\PosterUserData;
use app\common\service\PosterService;

class PosterFormLogic
{
    /**
     * 获取表单配置
     */
    public static function getFormConfig($configId)
    {
        try {
            $config = PosterTemplateConfig::where('id', $configId)
                ->where('status', PosterTemplateConfig::STATUS_ENABLED)
                ->find();
            
            if (!$config) {
                return null;
            }
            
            // 处理参数配置，只返回启用的参数
            $parameters = $config->parameters;
            $enabledParameters = [];
            
            if (is_array($parameters)) {
                foreach ($parameters as $param) {
                    if ($param['isEnabled']) {
                        // 移除不需要暴露给前端的字段
                        unset($param['elementUuid']);
                        $enabledParameters[] = $param;
                    }
                }
            }
            
            return [
                'id' => $config->id,
                'config_name' => $config->config_name,
                'config_description' => $config->config_description,
                'template_id' => $config->template_id,
                'template_title' => $config->template_title,
                'parameters' => $enabledParameters
            ];
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 提交表单数据
     */
    public static function submitForm($configId, $parameterValues, $userId = null, $sessionId = null, $isDraft = true)
    {
        try {
            // 验证配置是否存在
            $config = PosterTemplateConfig::find($configId);
            if (!$config || $config->status != PosterTemplateConfig::STATUS_ENABLED) {
                throw new \Exception('配置不存在或已禁用');
            }
            
            // 验证参数值
            if (!self::validateParameterValues($parameterValues, $config->parameters)) {
                throw new \Exception('参数值验证失败');
            }
            
            // 创建用户数据
            $posterService = new PosterService();
            $userData = $posterService->saveUserData(
                $configId,
                $parameterValues,
                $userId,
                $sessionId,
                $isDraft
            );
            
            if ($userData) {
                return [
                    'id' => $userData->id,
                    'config_id' => $userData->config_id,
                    'is_draft' => $userData->is_draft,
                    'created_at' => $userData->created_at
                ];
            }
            
            return false;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 生成预览
     */
    public static function generatePreview($dataId)
    {
        try {
            $posterService = new PosterService();
            $response = $posterService->generatePreview($dataId);
            
            if ($response->isSuccess()) {
                return [
                    'preview_url' => $response->getPreviewUrl(),
                    'success' => true
                ];
            } else {
                throw new \Exception($response->getMessage());
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 生成图片
     */
    public static function generateImage($dataId, $width = 1242, $height = 2208, $quality = 0.9)
    {
        try {
            $posterService = new PosterService();
            $response = $posterService->generateImage($dataId, $width, $height, $quality);
            
            if ($response->isSuccess()) {
                return [
                    'image_url' => $response->getImageUrl(),
                    'width' => $width,
                    'height' => $height,
                    'quality' => $quality,
                    'success' => true
                ];
            } else {
                throw new \Exception($response->getMessage());
            }
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 获取用户历史数据
     */
    public static function getUserHistory($userId = null, $sessionId = null, $page = 1, $limit = 10)
    {
        try {
            $where = [];
            
            if ($userId) {
                $where[] = ['user_id', '=', $userId];
            } elseif ($sessionId) {
                $where[] = ['session_id', '=', $sessionId];
            } else {
                return ['count' => 0, 'lists' => []];
            }
            
            $count = PosterUserData::where($where)->count();
            
            $lists = PosterUserData::where($where)
                ->with('config')
                ->page($page, $limit)
                ->order('created_at', 'desc')
                ->select();
            
            $result = [];
            foreach ($lists as $item) {
                $config = $item->config;
                $result[] = [
                    'id' => $item->id,
                    'config_id' => $item->config_id,
                    'config_name' => $config ? $config->config_name : '',
                    'template_title' => $config ? $config->template_title : '',
                    'parameter_values' => $item->parameter_values,
                    'is_draft' => $item->is_draft,
                    'preview_url' => $item->preview_url,
                    'generated_image_url' => $item->generated_image_url,
                    'created_at' => $item->created_at,
                    'updated_at' => $item->updated_at
                ];
            }
            
            return [
                'count' => $count,
                'lists' => $result
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 删除用户数据
     */
    public static function deleteUserData($dataId, $userId = null, $sessionId = null)
    {
        try {
            $where = ['id' => $dataId];
            
            if ($userId) {
                $where['user_id'] = $userId;
            } elseif ($sessionId) {
                $where['session_id'] = $sessionId;
            } else {
                return false;
            }
            
            $userData = PosterUserData::where($where)->find();
            if (!$userData) {
                return false;
            }
            
            return $userData->delete();
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取可用配置列表
     */
    public static function getConfigList($page = 1, $limit = 10, $keyword = '')
    {
        try {
            $where = ['status' => PosterTemplateConfig::STATUS_ENABLED];
            
            if (!empty($keyword)) {
                $where[] = ['config_name|template_title', 'like', '%' . $keyword . '%'];
            }
            
            $count = PosterTemplateConfig::where($where)->count();
            
            $lists = PosterTemplateConfig::where($where)
                ->field('id,config_name,config_description,template_title,created_at')
                ->page($page, $limit)
                ->order('created_at', 'desc')
                ->select();
            
            return [
                'count' => $count,
                'lists' => $lists
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 更新草稿数据
     */
    public static function updateDraft($dataId, $parameterValues, $userId = null, $sessionId = null)
    {
        try {
            $where = ['id' => $dataId, 'is_draft' => true];
            
            if ($userId) {
                $where['user_id'] = $userId;
            } elseif ($sessionId) {
                $where['session_id'] = $sessionId;
            } else {
                return false;
            }
            
            $userData = PosterUserData::where($where)->find();
            if (!$userData) {
                return false;
            }
            
            // 验证参数值
            $config = $userData->config;
            if (!$config || !self::validateParameterValues($parameterValues, $config->parameters)) {
                throw new \Exception('参数值验证失败');
            }
            
            $userData->parameter_values = $parameterValues;
            $result = $userData->save();
            
            if ($result) {
                return [
                    'id' => $userData->id,
                    'updated_at' => $userData->updated_at
                ];
            }
            
            return false;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 发布草稿
     */
    public static function publishDraft($dataId, $userId = null, $sessionId = null)
    {
        try {
            $where = ['id' => $dataId, 'is_draft' => true];
            
            if ($userId) {
                $where['user_id'] = $userId;
            } elseif ($sessionId) {
                $where['session_id'] = $sessionId;
            } else {
                return false;
            }
            
            $userData = PosterUserData::where($where)->find();
            if (!$userData) {
                return false;
            }
            
            $userData->is_draft = false;
            $result = $userData->save();
            
            if ($result) {
                return [
                    'id' => $userData->id,
                    'is_draft' => $userData->is_draft,
                    'updated_at' => $userData->updated_at
                ];
            }
            
            return false;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 验证参数值
     */
    private static function validateParameterValues($parameterValues, $parameters)
    {
        if (!is_array($parameterValues) || !is_array($parameters)) {
            return false;
        }
        
        foreach ($parameters as $param) {
            if (!$param['isEnabled']) {
                continue;
            }
            
            $paramName = $param['parameterName'];
            $value = $parameterValues[$paramName] ?? '';
            
            // 检查必填参数
            if ($param['isRequired'] && empty($value)) {
                return false;
            }
            
            // 验证参数值格式
            if (!empty($value) && !self::validateParameterValue($value, $param)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证单个参数值
     */
    private static function validateParameterValue($value, $param)
    {
        $type = $param['parameterType'];
        $rules = $param['validationRules'] ?? [];
        
        switch ($type) {
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
                
            case 'phone':
                $pattern = $rules['pattern'] ?? '/^1[3-9]\d{9}$/';
                return preg_match($pattern, $value);
                
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
                
            case 'number':
                if (!is_numeric($value)) {
                    return false;
                }
                $numValue = floatval($value);
                if (isset($rules['min']) && $numValue < $rules['min']) {
                    return false;
                }
                if (isset($rules['max']) && $numValue > $rules['max']) {
                    return false;
                }
                return true;
                
            case 'text':
            case 'textarea':
                $length = mb_strlen($value, 'utf-8');
                if (isset($rules['minLength']) && $length < $rules['minLength']) {
                    return false;
                }
                if (isset($rules['maxLength']) && $length > $rules['maxLength']) {
                    return false;
                }
                if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
                    return false;
                }
                return true;
                
            default:
                return true;
        }
    }
}
