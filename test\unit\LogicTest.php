<?php
/**
 * 逻辑层单元测试
 */

namespace Test\Unit;

use PHPUnit\Framework\TestCase;
use Test\Tools\TestHelper;
use app\admin\logic\PosterConfigLogic;
use app\api\logic\PosterFormLogic;

class LogicTest extends TestCase
{
    protected function setUp(): void
    {
        TestHelper::initTestEnvironment();
        TestHelper::insertTestData();
    }
    
    protected function tearDown(): void
    {
        TestHelper::cleanTestData();
    }
    
    /**
     * 测试配置逻辑层
     */
    public function testPosterConfigLogic()
    {
        // 测试获取配置列表
        $params = [
            'page' => 1,
            'limit' => 10,
            'keyword' => '个性化'
        ];
        
        $result = PosterConfigLogic::getConfigList($params);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('lists', $result);
        $this->assertGreaterThanOrEqual(0, $result['count']);
        $this->assertIsArray($result['lists']);
        
        // 测试添加配置
        $configData = [
            'template_id' => 'test-template-new',
            'template_title' => '新测试模板',
            'config_name' => '新测试配置',
            'config_description' => '这是一个新的测试配置',
            'parameters' => [
                [
                    'elementUuid' => 'new-uuid-001',
                    'parameterName' => 'new_param',
                    'parameterLabel' => '新参数',
                    'parameterType' => 'text',
                    'isRequired' => false,
                    'isEnabled' => true,
                    'defaultValue' => '新默认值'
                ]
            ]
        ];
        
        $config = PosterConfigLogic::addConfig($configData, 'test-admin');
        $this->assertNotFalse($config);
        
        // 测试获取配置详情
        $detail = PosterConfigLogic::getConfigDetail($config->id);
        $this->assertIsArray($detail);
        $this->assertEquals($configData['config_name'], $detail['config_name']);
        $this->assertArrayHasKey('parameter_stats', $detail);
        $this->assertArrayHasKey('user_data_stats', $detail);
        
        // 测试编辑配置
        $updateData = [
            'id' => $config->id,
            'config_name' => '更新后的配置名称',
            'config_description' => '更新后的描述'
        ];
        
        $result = PosterConfigLogic::editConfig($updateData);
        $this->assertTrue($result);
        
        // 测试切换状态
        $result = PosterConfigLogic::toggleStatus($config->id, 0);
        $this->assertTrue($result);
        
        $result = PosterConfigLogic::toggleStatus($config->id, 1);
        $this->assertTrue($result);
        
        // 测试复制配置
        $copiedConfig = PosterConfigLogic::copyConfig($config->id, 'test-admin');
        $this->assertNotFalse($copiedConfig);
        $this->assertNotEquals($config->id, $copiedConfig->id);
        $this->assertStringContains('副本', $copiedConfig->config_name);
        
        // 测试获取配置选项
        $options = PosterConfigLogic::getConfigOptions();
        $this->assertIsArray($options);
        
        // 测试参数验证
        $validParameters = [
            [
                'elementUuid' => 'test-uuid',
                'parameterName' => 'valid_param',
                'parameterLabel' => '有效参数',
                'parameterType' => 'text',
                'isRequired' => true,
                'isEnabled' => true
            ]
        ];
        
        $this->assertTrue(PosterConfigLogic::validateParameters($validParameters));
        
        $invalidParameters = [
            [
                'elementUuid' => 'test-uuid',
                'parameterName' => 'invalid_param'
                // 缺少必需字段
            ]
        ];
        
        $this->assertFalse(PosterConfigLogic::validateParameters($invalidParameters));
        
        // 测试获取统计信息
        $stats = PosterConfigLogic::getConfigStats();
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total', $stats);
        $this->assertArrayHasKey('enabled', $stats);
        $this->assertArrayHasKey('disabled', $stats);
        $this->assertArrayHasKey('recent', $stats);
        $this->assertArrayHasKey('top_templates', $stats);
    }
    
    /**
     * 测试表单逻辑层
     */
    public function testPosterFormLogic()
    {
        // 测试获取表单配置
        $configId = 'test-config-001';
        $formConfig = PosterFormLogic::getFormConfig($configId);
        
        $this->assertIsArray($formConfig);
        $this->assertEquals($configId, $formConfig['id']);
        $this->assertArrayHasKey('config_name', $formConfig);
        $this->assertArrayHasKey('parameters', $formConfig);
        $this->assertIsArray($formConfig['parameters']);
        
        // 验证只返回启用的参数
        foreach ($formConfig['parameters'] as $param) {
            $this->assertTrue($param['isEnabled']);
            $this->assertArrayNotHasKey('elementUuid', $param); // 不应该暴露给前端
        }
        
        // 测试提交表单数据
        $parameterValues = [
            'greeting' => '你好，测试！'
        ];
        
        $result = PosterFormLogic::submitForm(
            $configId,
            $parameterValues,
            'test-user-001',
            null,
            true
        );
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('config_id', $result);
        $this->assertTrue($result['is_draft']);
        
        $dataId = $result['id'];
        
        // 测试更新草稿
        $updatedValues = [
            'greeting' => '更新后的问候语'
        ];
        
        $updateResult = PosterFormLogic::updateDraft(
            $dataId,
            $updatedValues,
            'test-user-001',
            null
        );
        
        $this->assertIsArray($updateResult);
        $this->assertEquals($dataId, $updateResult['id']);
        
        // 测试发布草稿
        $publishResult = PosterFormLogic::publishDraft(
            $dataId,
            'test-user-001',
            null
        );
        
        $this->assertIsArray($publishResult);
        $this->assertEquals($dataId, $publishResult['id']);
        $this->assertFalse($publishResult['is_draft']);
        
        // 测试获取用户历史数据
        $history = PosterFormLogic::getUserHistory('test-user-001', null, 1, 10);
        
        $this->assertIsArray($history);
        $this->assertArrayHasKey('count', $history);
        $this->assertArrayHasKey('lists', $history);
        $this->assertGreaterThan(0, $history['count']);
        
        // 测试获取可用配置列表
        $configList = PosterFormLogic::getConfigList(1, 10, '');
        
        $this->assertIsArray($configList);
        $this->assertArrayHasKey('count', $configList);
        $this->assertArrayHasKey('lists', $configList);
        $this->assertGreaterThan(0, $configList['count']);
        
        // 测试删除用户数据
        $deleteResult = PosterFormLogic::deleteUserData(
            $dataId,
            'test-user-001',
            null
        );
        
        $this->assertTrue($deleteResult);
        
        // 验证数据已删除
        $historyAfterDelete = PosterFormLogic::getUserHistory('test-user-001', null, 1, 10);
        $this->assertEquals(0, $historyAfterDelete['count']);
    }
    
    /**
     * 测试表单验证逻辑
     */
    public function testFormValidation()
    {
        // 测试有效的参数值
        $validConfig = [
            'parameters' => [
                [
                    'parameterName' => 'email',
                    'parameterType' => 'email',
                    'isRequired' => true,
                    'isEnabled' => true
                ],
                [
                    'parameterName' => 'phone',
                    'parameterType' => 'phone',
                    'isRequired' => false,
                    'isEnabled' => true,
                    'validationRules' => [
                        'pattern' => '/^1[3-9]\d{9}$/'
                    ]
                ],
                [
                    'parameterName' => 'age',
                    'parameterType' => 'number',
                    'isRequired' => false,
                    'isEnabled' => true,
                    'validationRules' => [
                        'min' => 0,
                        'max' => 120
                    ]
                ]
            ]
        ];
        
        // 测试有效值
        $validValues = [
            'email' => '<EMAIL>',
            'phone' => '13800138000',
            'age' => '25'
        ];
        
        $reflection = new \ReflectionClass(PosterFormLogic::class);
        $method = $reflection->getMethod('validateParameterValues');
        $method->setAccessible(true);
        
        $result = $method->invoke(null, $validValues, $validConfig['parameters']);
        $this->assertTrue($result);
        
        // 测试无效邮箱
        $invalidEmailValues = [
            'email' => 'invalid-email',
            'phone' => '13800138000',
            'age' => '25'
        ];
        
        $result = $method->invoke(null, $invalidEmailValues, $validConfig['parameters']);
        $this->assertFalse($result);
        
        // 测试无效手机号
        $invalidPhoneValues = [
            'email' => '<EMAIL>',
            'phone' => '12345',
            'age' => '25'
        ];
        
        $result = $method->invoke(null, $invalidPhoneValues, $validConfig['parameters']);
        $this->assertFalse($result);
        
        // 测试超出范围的数字
        $invalidAgeValues = [
            'email' => '<EMAIL>',
            'phone' => '13800138000',
            'age' => '150'
        ];
        
        $result = $method->invoke(null, $invalidAgeValues, $validConfig['parameters']);
        $this->assertFalse($result);
        
        // 测试缺少必填字段
        $missingRequiredValues = [
            'phone' => '13800138000',
            'age' => '25'
            // 缺少必填的email
        ];
        
        $result = $method->invoke(null, $missingRequiredValues, $validConfig['parameters']);
        $this->assertFalse($result);
    }
    
    /**
     * 测试错误处理
     */
    public function testErrorHandling()
    {
        // 测试获取不存在的配置
        $nonExistentConfig = PosterFormLogic::getFormConfig('non-existent-config');
        $this->assertNull($nonExistentConfig);
        
        // 测试提交到不存在的配置
        $this->expectException(\Exception::class);
        PosterFormLogic::submitForm(
            'non-existent-config',
            ['test' => 'value'],
            'test-user',
            null,
            true
        );
        
        // 测试更新不存在的数据
        $result = PosterFormLogic::updateDraft(
            'non-existent-data',
            ['test' => 'value'],
            'test-user',
            null
        );
        $this->assertFalse($result);
        
        // 测试删除不存在的数据
        $result = PosterFormLogic::deleteUserData(
            'non-existent-data',
            'test-user',
            null
        );
        $this->assertFalse($result);
    }
}
