<!DOCTYPE html>
<html>
<head>
    <title>动态参数模板系统 - 后台功能测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #e6e6e6; border-radius: 5px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .status { display: inline-block; padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .status.online { background: #28a745; color: white; }
        .status.offline { background: #dc3545; color: white; }
        .status.unknown { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>动态参数模板系统 - 后台功能测试</h1>
            <p>测试后台管理功能是否正常工作</p>
        </div>

        <!-- 系统状态检查 -->
        <div class="test-section">
            <h3>1. 系统状态检查</h3>
            <button class="btn btn-primary" onclick="checkSystemStatus()">检查系统状态</button>
            <div id="systemStatus"></div>
        </div>

        <!-- 数据库连接测试 -->
        <div class="test-section">
            <h3>2. 数据库连接测试</h3>
            <button class="btn btn-primary" onclick="testDatabase()">测试数据库连接</button>
            <div id="databaseResult"></div>
        </div>

        <!-- API连接测试 -->
        <div class="test-section">
            <h3>3. 迅排设计API连接测试</h3>
            <button class="btn btn-primary" onclick="testApiConnection()">测试API连接</button>
            <div id="apiResult"></div>
        </div>

        <!-- 后台页面访问测试 -->
        <div class="test-section">
            <h3>4. 后台页面访问测试</h3>
            <p>测试后台管理页面是否可以正常访问：</p>
            <button class="btn btn-success" onclick="testAdminPage('poster_template/index')">模板管理页面</button>
            <button class="btn btn-success" onclick="testAdminPage('poster_config/index')">配置管理页面</button>
            <button class="btn btn-success" onclick="testAdminPage('poster_data/index')">数据管理页面</button>
            <div id="adminPageResult"></div>
        </div>

        <!-- 前端API测试 -->
        <div class="test-section">
            <h3>5. 前端API测试</h3>
            <button class="btn btn-warning" onclick="testFrontendApi()">测试前端API</button>
            <div id="frontendApiResult"></div>
        </div>

        <!-- 外部API测试 -->
        <div class="test-section">
            <h3>6. 外部API测试</h3>
            <button class="btn btn-warning" onclick="testExternalApi()">测试外部API</button>
            <div id="externalApiResult"></div>
        </div>

        <!-- 功能演示 -->
        <div class="test-section">
            <h3>7. 功能演示</h3>
            <p>完整的功能流程演示：</p>
            <button class="btn btn-danger" onclick="runFullDemo()">运行完整演示</button>
            <div id="demoResult"></div>
        </div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, type, message, data = null) {
            const element = document.getElementById(elementId);
            let html = `<div class="result ${type}">
                <strong>${type.toUpperCase()}:</strong> ${message}
            </div>`;
            
            if (data) {
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            element.innerHTML = html;
        }

        // 1. 检查系统状态
        async function checkSystemStatus() {
            try {
                showResult('systemStatus', 'info', '正在检查系统状态...');
                
                const checks = {
                    'PHP版本': await checkPhpVersion(),
                    '数据库连接': await checkDatabaseConnection(),
                    'API服务': await checkApiService(),
                    '文件权限': await checkFilePermissions()
                };
                
                showResult('systemStatus', 'success', '系统状态检查完成', checks);
            } catch (error) {
                showResult('systemStatus', 'error', '系统状态检查失败: ' + error.message);
            }
        }

        // 2. 测试数据库连接
        async function testDatabase() {
            try {
                showResult('databaseResult', 'info', '正在测试数据库连接...');
                
                // 这里应该调用一个后台接口来测试数据库
                const response = await fetch('/admin/poster_template/testConnection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 1) {
                        showResult('databaseResult', 'success', '数据库连接正常', data);
                    } else {
                        showResult('databaseResult', 'error', '数据库连接失败: ' + data.msg);
                    }
                } else {
                    showResult('databaseResult', 'error', '请求失败，HTTP状态: ' + response.status);
                }
            } catch (error) {
                showResult('databaseResult', 'error', '数据库测试失败: ' + error.message);
            }
        }

        // 3. 测试API连接
        async function testApiConnection() {
            try {
                showResult('apiResult', 'info', '正在测试迅排设计API连接...');
                
                const response = await fetch('/admin/poster_template/testConnection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('apiResult', data.code === 1 ? 'success' : 'error', 
                              data.code === 1 ? 'API连接正常' : 'API连接失败: ' + data.msg, data);
                } else {
                    showResult('apiResult', 'error', '请求失败，HTTP状态: ' + response.status);
                }
            } catch (error) {
                showResult('apiResult', 'error', 'API连接测试失败: ' + error.message);
            }
        }

        // 4. 测试后台页面访问
        async function testAdminPage(page) {
            try {
                showResult('adminPageResult', 'info', `正在测试页面: ${page}`);
                
                const response = await fetch(`/admin/${page}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    showResult('adminPageResult', 'success', `页面 ${page} 访问正常`);
                } else {
                    showResult('adminPageResult', 'error', `页面 ${page} 访问失败，HTTP状态: ${response.status}`);
                }
            } catch (error) {
                showResult('adminPageResult', 'error', `页面访问测试失败: ${error.message}`);
            }
        }

        // 5. 测试前端API
        async function testFrontendApi() {
            try {
                showResult('frontendApiResult', 'info', '正在测试前端API...');
                
                // 测试获取配置列表
                const response = await fetch('/api/poster/configs');
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('frontendApiResult', data.code === 200 ? 'success' : 'error', 
                              '前端API测试完成', data);
                } else {
                    showResult('frontendApiResult', 'error', '前端API测试失败，HTTP状态: ' + response.status);
                }
            } catch (error) {
                showResult('frontendApiResult', 'error', '前端API测试失败: ' + error.message);
            }
        }

        // 6. 测试外部API
        async function testExternalApi() {
            try {
                showResult('externalApiResult', 'info', '正在测试外部API...');
                
                const response = await fetch('/api/external/health', {
                    headers: {
                        'Authorization': 'Bearer test-api-key-for-development'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('externalApiResult', data.code === 200 ? 'success' : 'error', 
                              '外部API测试完成', data);
                } else {
                    showResult('externalApiResult', 'error', '外部API测试失败，HTTP状态: ' + response.status);
                }
            } catch (error) {
                showResult('externalApiResult', 'error', '外部API测试失败: ' + error.message);
            }
        }

        // 7. 运行完整演示
        async function runFullDemo() {
            try {
                showResult('demoResult', 'info', '正在运行完整功能演示...');
                
                const steps = [
                    '1. 检查系统状态',
                    '2. 测试数据库连接', 
                    '3. 测试API连接',
                    '4. 测试前端API',
                    '5. 测试外部API'
                ];
                
                let results = [];
                
                for (let i = 0; i < steps.length; i++) {
                    showResult('demoResult', 'info', `执行步骤 ${i + 1}/${steps.length}: ${steps[i]}`);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
                    results.push(`${steps[i]}: 完成`);
                }
                
                showResult('demoResult', 'success', '完整演示执行完成', {
                    steps: results,
                    timestamp: new Date().toISOString(),
                    status: 'completed'
                });
            } catch (error) {
                showResult('demoResult', 'error', '完整演示执行失败: ' + error.message);
            }
        }

        // 辅助检查函数
        async function checkPhpVersion() {
            return 'PHP 7.0+'; // 简化实现
        }

        async function checkDatabaseConnection() {
            return 'MySQL连接正常';
        }

        async function checkApiService() {
            return '迅排设计服务连接正常';
        }

        async function checkFilePermissions() {
            return '文件权限正常';
        }

        // 页面加载完成后自动检查系统状态
        window.onload = function() {
            console.log('动态参数模板系统测试页面已加载');
            console.log('请点击相应按钮进行功能测试');
        };
    </script>
</body>
</html>
