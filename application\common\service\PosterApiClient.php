<?php
/**
 * 迅排设计API客户端
 */

namespace app\common\service;

use think\facade\Log;
use think\facade\Cache;

class PosterApiClient
{
    private $baseUrl;
    private $timeout;
    private $retryTimes;
    private $retryDelay;
    private $cacheEnabled;
    private $cacheTtl;
    
    public function __construct($config = [])
    {
        $this->baseUrl = $config['base_url'] ?? 'http://localhost:7001';
        $this->timeout = $config['timeout'] ?? 30;
        $this->retryTimes = $config['retry_times'] ?? 3;
        $this->retryDelay = $config['retry_delay'] ?? 1000;
        $this->cacheEnabled = $config['cache_enabled'] ?? true;
        $this->cacheTtl = $config['cache_ttl'] ?? 600;
    }
    
    /**
     * 获取模板列表
     */
    public function getTemplates($params = [])
    {
        $cacheKey = 'poster_templates_' . md5(serialize($params));
        
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $url = '/api/templates';
        $response = $this->request('GET', $url, $params);
        
        if ($this->cacheEnabled && $response['code'] == 200) {
            Cache::set($cacheKey, $response, $this->cacheTtl);
        }
        
        return $response;
    }
    
    /**
     * 获取模板详情
     */
    public function getTemplate($templateId)
    {
        $cacheKey = 'poster_template_' . $templateId;
        
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $url = '/api/template';
        $params = ['id' => $templateId];
        $response = $this->request('GET', $url, $params);
        
        if ($this->cacheEnabled && $response['code'] == 200) {
            Cache::set($cacheKey, $response, $this->cacheTtl);
        }
        
        return $response;
    }
    
    /**
     * 解析模板
     */
    public function parseTemplate($templateId)
    {
        $cacheKey = 'poster_parse_' . $templateId;
        
        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }
        
        $url = '/api/template/parse';
        $data = ['templateId' => $templateId];
        $response = $this->request('POST', $url, [], $data);
        
        if ($this->cacheEnabled && $response['code'] == 200) {
            // 解析结果缓存更长时间
            Cache::set($cacheKey, $response, $this->cacheTtl * 6);
        }
        
        return $response;
    }
    
    /**
     * 生成预览
     */
    public function generatePreview($templateId, $parameterDataId)
    {
        $url = '/api/parameter/preview';
        $data = [
            'templateId' => $templateId,
            'parameterDataId' => $parameterDataId
        ];
        
        return $this->request('POST', $url, [], $data);
    }
    
    /**
     * 执行参数替换
     */
    public function replaceParameters($templateId, $parameterDataId)
    {
        $url = '/api/parameter/replace';
        $data = [
            'templateId' => $templateId,
            'parameterDataId' => $parameterDataId
        ];
        
        return $this->request('POST', $url, [], $data);
    }
    
    /**
     * 生成图片
     */
    public function generateImage($params)
    {
        $url = '/api/screenshots';
        return $this->request('GET', $url, $params);
    }
    
    /**
     * 批量生成图片
     */
    public function batchGenerate($dataIds, $outputOptions)
    {
        $url = '/api/parameter/batch-generate';
        $data = [
            'dataIds' => $dataIds,
            'outputOptions' => $outputOptions
        ];
        
        return $this->request('POST', $url, [], $data);
    }
    
    /**
     * 查询批量任务状态
     */
    public function getBatchStatus($batchId)
    {
        $url = '/api/parameter/batch-status/' . $batchId;
        return $this->request('GET', $url);
    }
    
    /**
     * 健康检查
     */
    public function healthCheck()
    {
        $url = '/health';
        return $this->request('GET', $url);
    }
    
    /**
     * 发送HTTP请求
     */
    private function request($method, $url, $params = [], $data = [])
    {
        $fullUrl = $this->baseUrl . $url;
        
        if ($method === 'GET' && !empty($params)) {
            $fullUrl .= '?' . http_build_query($params);
        }
        
        $options = [
            CURLOPT_URL => $fullUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'User-Agent: LikeShop-PosterClient/1.0'
            ]
        ];
        
        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            if (!empty($data)) {
                $options[CURLOPT_POSTFIELDS] = json_encode($data);
            }
        }
        
        return $this->executeRequest($options, $fullUrl);
    }
    
    /**
     * 执行请求（带重试机制）
     */
    private function executeRequest($options, $url)
    {
        $attempt = 0;
        
        while ($attempt < $this->retryTimes) {
            $attempt++;
            
            $ch = curl_init();
            curl_setopt_array($ch, $options);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                Log::error("PosterAPI Request Error (Attempt {$attempt}): {$error}", [
                    'url' => $url,
                    'options' => $options
                ]);
                
                if ($attempt < $this->retryTimes) {
                    usleep($this->retryDelay * 1000); // 转换为微秒
                    continue;
                }
                
                return [
                    'code' => 500,
                    'message' => 'Network error: ' . $error,
                    'data' => null
                ];
            }
            
            $decodedResponse = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error("PosterAPI JSON Decode Error: " . json_last_error_msg(), [
                    'url' => $url,
                    'response' => $response
                ]);
                
                return [
                    'code' => 500,
                    'message' => 'Invalid JSON response',
                    'data' => null
                ];
            }
            
            // 记录请求日志
            Log::info("PosterAPI Request", [
                'url' => $url,
                'http_code' => $httpCode,
                'response_code' => $decodedResponse['code'] ?? 'unknown',
                'attempt' => $attempt
            ]);
            
            // 如果是服务器错误且还有重试次数，则重试
            if ($httpCode >= 500 && $attempt < $this->retryTimes) {
                usleep($this->retryDelay * 1000);
                continue;
            }
            
            return $decodedResponse;
        }
        
        return [
            'code' => 500,
            'message' => 'Max retry attempts reached',
            'data' => null
        ];
    }
    
    /**
     * 清理缓存
     */
    public function clearCache($pattern = null)
    {
        if ($pattern) {
            // 清理特定模式的缓存
            Cache::clear($pattern);
        } else {
            // 清理所有poster相关缓存
            Cache::clear('poster_*');
        }
    }
    
    /**
     * 获取缓存统计
     */
    public function getCacheStats()
    {
        // 这里可以实现缓存统计逻辑
        return [
            'enabled' => $this->cacheEnabled,
            'ttl' => $this->cacheTtl,
            'hit_rate' => 0 // 需要实现缓存命中率统计
        ];
    }
}
