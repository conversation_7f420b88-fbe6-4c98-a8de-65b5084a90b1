<?php
/**
 * API客户端单元测试
 */

namespace Test\Unit;

use PHPUnit\Framework\TestCase;
use app\common\service\PosterApiClient;
use app\common\service\PosterApiFactory;
use app\common\service\PosterApiResponse;

class ApiClientTest extends TestCase
{
    private $apiClient;
    private $testConfig;
    
    protected function setUp(): void
    {
        $this->testConfig = include __DIR__ . '/../config/poster_api.php';
        $this->apiClient = new PosterApiClient($this->testConfig);
    }
    
    /**
     * 测试API客户端初始化
     */
    public function testApiClientInitialization()
    {
        $this->assertInstanceOf(PosterApiClient::class, $this->apiClient);
    }
    
    /**
     * 测试工厂类创建实例
     */
    public function testFactoryCreateInstance()
    {
        $instance1 = PosterApiFactory::getInstance();
        $instance2 = PosterApiFactory::getInstance();
        
        // 测试单例模式
        $this->assertSame($instance1, $instance2);
        
        // 测试创建新实例
        $newInstance = PosterApiFactory::create(['timeout' => 60]);
        $this->assertNotSame($instance1, $newInstance);
    }
    
    /**
     * 测试获取模板列表（Mock模式）
     */
    public function testGetTemplatesList()
    {
        if (!$this->testConfig['use_mock']) {
            $this->markTestSkipped('Mock mode is disabled');
        }
        
        // 模拟响应数据
        $mockResponse = $this->testConfig['mock']['templates'];
        
        // 这里应该使用Mock框架，简化起见直接测试响应处理
        $response = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $mockResponse,
                'total' => count($mockResponse),
                'page' => 1,
                'pageSize' => 10,
                'totalPages' => 1
            ]
        ];
        
        $apiResponse = new PosterApiResponse($response);
        
        $this->assertTrue($apiResponse->isSuccess());
        $this->assertEquals(200, $apiResponse->getCode());
        $this->assertIsArray($apiResponse->getTemplateList());
        $this->assertNotEmpty($apiResponse->getTemplateList());
        
        $paginationInfo = $apiResponse->getPaginationInfo();
        $this->assertIsArray($paginationInfo);
        $this->assertArrayHasKey('total', $paginationInfo);
        $this->assertArrayHasKey('page', $paginationInfo);
    }
    
    /**
     * 测试模板解析响应
     */
    public function testTemplateParseResponse()
    {
        $mockParseResult = $this->testConfig['mock']['parse_results']['2'];
        
        $response = [
            'code' => 200,
            'message' => 'success',
            'data' => $mockParseResult
        ];
        
        $apiResponse = new PosterApiResponse($response);
        
        $this->assertTrue($apiResponse->isSuccess());
        
        $parseResult = $apiResponse->getParseResult();
        $this->assertIsArray($parseResult);
        $this->assertArrayHasKey('templateId', $parseResult);
        $this->assertArrayHasKey('templateTitle', $parseResult);
        $this->assertArrayHasKey('parameterCandidates', $parseResult);
        
        $candidates = $apiResponse->getParameterCandidates();
        $this->assertIsArray($candidates);
        $this->assertNotEmpty($candidates);
        
        // 验证参数候选项结构
        $firstCandidate = $candidates[0];
        $this->assertArrayHasKey('elementUuid', $firstCandidate);
        $this->assertArrayHasKey('suggestedName', $firstCandidate);
        $this->assertArrayHasKey('suggestedLabel', $firstCandidate);
        $this->assertArrayHasKey('suggestedType', $firstCandidate);
    }
    
    /**
     * 测试错误响应处理
     */
    public function testErrorResponse()
    {
        $errorResponse = [
            'code' => 404,
            'message' => 'Template not found',
            'data' => null
        ];
        
        $apiResponse = new PosterApiResponse($errorResponse);
        
        $this->assertFalse($apiResponse->isSuccess());
        $this->assertEquals(404, $apiResponse->getCode());
        $this->assertEquals('Template not found', $apiResponse->getMessage());
        $this->assertNull($apiResponse->getData());
        
        $error = $apiResponse->getError();
        $this->assertIsArray($error);
        $this->assertEquals(404, $error['code']);
        $this->assertEquals('Template not found', $error['message']);
    }
    
    /**
     * 测试预览URL获取
     */
    public function testPreviewUrlResponse()
    {
        $response = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'previewUrl' => 'http://localhost:7001/preview/parameter/test-data-001',
                'success' => true
            ]
        ];
        
        $apiResponse = new PosterApiResponse($response);
        
        $this->assertTrue($apiResponse->isSuccess());
        $this->assertNotNull($apiResponse->getPreviewUrl());
        $this->assertStringContains('preview', $apiResponse->getPreviewUrl());
    }
    
    /**
     * 测试图片生成响应
     */
    public function testImageGenerationResponse()
    {
        $response = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'url' => 'http://localhost:7001/static/generated/test-data-001_1242x2208.jpg',
                'width' => 1242,
                'height' => 2208,
                'fileSize' => 256000,
                'generatedAt' => '2025-01-16T10:00:00Z'
            ]
        ];
        
        $apiResponse = new PosterApiResponse($response);
        
        $this->assertTrue($apiResponse->isSuccess());
        $this->assertNotNull($apiResponse->getImageUrl());
        $this->assertStringContains('.jpg', $apiResponse->getImageUrl());
    }
    
    /**
     * 测试批量任务响应
     */
    public function testBatchTaskResponse()
    {
        // 测试批量任务创建响应
        $createResponse = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'batchId' => 'batch_1755432640277_xxx',
                'status' => 'processing',
                'totalItems' => 2,
                'message' => '批量任务已创建，正在处理中'
            ]
        ];
        
        $apiResponse = new PosterApiResponse($createResponse);
        
        $this->assertTrue($apiResponse->isSuccess());
        $this->assertNotNull($apiResponse->getBatchId());
        
        // 测试批量任务状态响应
        $statusResponse = [
            'code' => 200,
            'message' => 'success',
            'data' => [
                'batchId' => 'batch_1755432640277_xxx',
                'status' => 'completed',
                'progress' => 100,
                'totalItems' => 2,
                'completedItems' => 2,
                'failedItems' => 0,
                'results' => [
                    [
                        'dataId' => 'test-data-123',
                        'status' => 'success',
                        'imageUrl' => 'http://localhost:7001/static/generated/test-data-123_1242x2208.jpg'
                    ]
                ]
            ]
        ];
        
        $statusApiResponse = new PosterApiResponse($statusResponse);
        $batchStatus = $statusApiResponse->getBatchStatus();
        
        $this->assertIsArray($batchStatus);
        $this->assertEquals('completed', $batchStatus['status']);
        $this->assertEquals(100, $batchStatus['progress']);
        $this->assertEquals(2, $batchStatus['totalItems']);
        $this->assertEquals(2, $batchStatus['completedItems']);
        $this->assertEquals(0, $batchStatus['failedItems']);
    }
    
    /**
     * 测试响应转换方法
     */
    public function testResponseConversion()
    {
        $response = [
            'code' => 200,
            'message' => 'success',
            'data' => ['test' => 'data']
        ];
        
        $apiResponse = new PosterApiResponse($response);
        
        // 测试转换为数组
        $array = $apiResponse->toArray();
        $this->assertIsArray($array);
        $this->assertArrayHasKey('success', $array);
        $this->assertTrue($array['success']);
        
        // 测试转换为JSON
        $json = $apiResponse->toJson();
        $this->assertJson($json);
        
        $decoded = json_decode($json, true);
        $this->assertEquals($array, $decoded);
        
        // 测试魔术方法
        $stringified = (string) $apiResponse;
        $this->assertEquals($json, $stringified);
    }
    
    /**
     * 测试异常抛出
     */
    public function testThrowIfFailed()
    {
        $errorResponse = [
            'code' => 500,
            'message' => 'Internal server error',
            'data' => null
        ];
        
        $apiResponse = new PosterApiResponse($errorResponse);
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Internal server error');
        $this->expectExceptionCode(500);
        
        $apiResponse->throwIfFailed();
    }
    
    /**
     * 测试自定义异常消息
     */
    public function testThrowIfFailedWithCustomMessage()
    {
        $errorResponse = [
            'code' => 400,
            'message' => 'Bad request',
            'data' => null
        ];
        
        $apiResponse = new PosterApiResponse($errorResponse);
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Custom error message');
        
        $apiResponse->throwIfFailed('Custom error message');
    }
}
