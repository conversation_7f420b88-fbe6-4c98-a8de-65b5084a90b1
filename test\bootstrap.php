<?php
/**
 * 测试引导文件
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 定义根目录
define('ROOT_PATH', __DIR__ . '/../');

// 定义测试目录
define('TEST_PATH', __DIR__ . '/');

// 加载框架引导文件
require_once ROOT_PATH . 'thinkphp/base.php';

// 加载测试配置
$testConfig = [
    // 数据库配置
    'database' => include TEST_PATH . 'config/database.php',
    
    // 迅排设计API配置
    'poster' => include TEST_PATH . 'config/poster_api.php',
    
    // 应用配置
    'app_debug' => true,
    'app_trace' => false,
    
    // 日志配置
    'log' => [
        'type' => 'File',
        'path' => TEST_PATH . 'logs/',
        'level' => ['error', 'warning', 'info'],
        'file_size' => 2097152,
        'time_format' => 'Y-m-d H:i:s',
    ],
    
    // 缓存配置
    'cache' => [
        'type' => 'File',
        'path' => TEST_PATH . 'cache/',
        'prefix' => 'test_',
        'expire' => 3600,
    ],
    
    // 会话配置
    'session' => [
        'type' => 'File',
        'path' => TEST_PATH . 'session/',
        'prefix' => 'test_',
        'expire' => 3600,
    ]
];

// 应用配置
\think\Config::set($testConfig);

// 初始化应用
\think\App::initCommon();

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 创建必要的目录
$directories = [
    TEST_PATH . 'logs',
    TEST_PATH . 'cache',
    TEST_PATH . 'session',
    TEST_PATH . 'coverage-html',
    TEST_PATH . 'data/fixtures',
    TEST_PATH . 'data/templates',
    TEST_PATH . 'data/parameters'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 注册自动加载
spl_autoload_register(function ($class) {
    // 处理测试命名空间
    if (strpos($class, 'Test\\') === 0) {
        $file = TEST_PATH . str_replace(['Test\\', '\\'], ['', '/'], $class) . '.php';
        if (file_exists($file)) {
            require_once $file;
        }
    }
});

// 设置全局测试变量
$GLOBALS['test_config'] = $testConfig;
$GLOBALS['test_start_time'] = microtime(true);

// 输出测试环境信息
echo "=== 动态参数模板系统测试环境 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "ThinkPHP版本: " . \think\App::version() . "\n";
echo "测试目录: " . TEST_PATH . "\n";
echo "数据库: " . $testConfig['database']['database'] . "\n";
echo "API地址: " . $testConfig['poster']['base_url'] . "\n";
echo "Mock模式: " . ($testConfig['poster']['use_mock'] ? '启用' : '禁用') . "\n";
echo "=====================================\n\n";

// 注册测试结束回调
register_shutdown_function(function () {
    $endTime = microtime(true);
    $duration = round($endTime - $GLOBALS['test_start_time'], 2);
    echo "\n=== 测试完成 ===\n";
    echo "总耗时: {$duration}秒\n";
    echo "内存使用: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . "MB\n";
    echo "================\n";
});

// 定义测试辅助函数
if (!function_exists('test_log')) {
    /**
     * 测试日志函数
     */
    function test_log($message, $level = 'info') {
        $logFile = TEST_PATH . 'logs/test.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}

if (!function_exists('test_dump')) {
    /**
     * 测试数据输出函数
     */
    function test_dump($data, $label = '') {
        if ($label) {
            echo "=== {$label} ===\n";
        }
        var_dump($data);
        echo "\n";
    }
}

if (!function_exists('test_assert_array_structure')) {
    /**
     * 断言数组结构
     */
    function test_assert_array_structure($expected, $actual, $message = '') {
        foreach ($expected as $key) {
            if (!array_key_exists($key, $actual)) {
                throw new \Exception($message ?: "Array missing key: {$key}");
            }
        }
        return true;
    }
}

// 初始化测试数据库连接
try {
    $db = \think\Db::connect($testConfig['database']);
    $db->query('SELECT 1');
    echo "数据库连接成功\n";
} catch (\Exception $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
    echo "请确保测试数据库已创建并配置正确\n";
    exit(1);
}

// 检查迅排设计API连接（如果不是Mock模式）
if (!$testConfig['poster']['use_mock']) {
    try {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $testConfig['poster']['base_url'] . '/health',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_CONNECTTIMEOUT => 3
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            echo "迅排设计API连接成功\n";
        } else {
            echo "迅排设计API连接失败，HTTP状态码: {$httpCode}\n";
            echo "将使用Mock模式进行测试\n";
            \think\Config::set('poster.use_mock', true);
        }
    } catch (\Exception $e) {
        echo "迅排设计API连接异常: " . $e->getMessage() . "\n";
        echo "将使用Mock模式进行测试\n";
        \think\Config::set('poster.use_mock', true);
    }
} else {
    echo "使用Mock模式进行测试\n";
}

echo "\n测试环境初始化完成\n\n";
