<?php
/**
 * 参数配置管理控制器
 */

namespace app\admin\controller;

use app\admin\logic\PosterConfigLogic;

class PosterConfig extends AdminBase
{
    /**
     * 配置列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return $this->_success('', PosterConfigLogic::getConfigList($get));
        }
        
        return $this->fetch();
    }
    
    /**
     * 添加配置
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, 'app\admin\validate\PosterConfig.add');
            if ($result !== true) {
                return $this->_error($result);
            }
            
            $result = PosterConfigLogic::addConfig($post, $this->admin_info['id']);
            if ($result) {
                return $this->_success('添加成功');
            } else {
                return $this->_error('添加失败');
            }
        }
        
        return $this->fetch();
    }
    
    /**
     * 编辑配置
     */
    public function edit()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, 'app\admin\validate\PosterConfig.edit');
            if ($result !== true) {
                return $this->_error($result);
            }
            
            $result = PosterConfigLogic::editConfig($post);
            if ($result) {
                return $this->_success('编辑成功');
            } else {
                return $this->_error('编辑失败');
            }
        }
        
        $id = $this->request->get('id');
        $config = PosterConfigLogic::getConfigDetail($id);
        
        if (!$config) {
            return $this->_error('配置不存在');
        }
        
        $this->assign('config', $config);
        return $this->fetch();
    }
    
    /**
     * 删除配置
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            
            if (empty($id)) {
                return $this->_error('参数错误');
            }
            
            $result = PosterConfigLogic::delConfig($id);
            if ($result) {
                return $this->_success('删除成功');
            } else {
                return $this->_error('删除失败');
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 配置详情
     */
    public function detail()
    {
        $id = $this->request->param('id');
        $config = PosterConfigLogic::getConfigDetail($id);
        
        if (!$config) {
            return $this->_error('配置不存在');
        }
        
        if ($this->request->isAjax()) {
            return $this->_success('获取成功', $config);
        }
        
        $this->assign('config', $config);
        return $this->fetch();
    }
    
    /**
     * 启用/禁用配置
     */
    public function toggleStatus()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            $status = $this->request->post('status');
            
            if (empty($id) || !in_array($status, [0, 1])) {
                return $this->_error('参数错误');
            }
            
            $result = PosterConfigLogic::toggleStatus($id, $status);
            if ($result) {
                return $this->_success('操作成功');
            } else {
                return $this->_error('操作失败');
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 预览配置表单
     */
    public function preview()
    {
        $id = $this->request->param('id');
        $config = PosterConfigLogic::getConfigDetail($id);
        
        if (!$config) {
            return $this->_error('配置不存在');
        }
        
        if ($this->request->isAjax()) {
            return $this->_success('获取成功', $config);
        }
        
        $this->assign('config', $config);
        return $this->fetch();
    }
    
    /**
     * 复制配置
     */
    public function copy()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            
            if (empty($id)) {
                return $this->_error('参数错误');
            }
            
            $result = PosterConfigLogic::copyConfig($id, $this->admin_info['id']);
            if ($result) {
                return $this->_success('复制成功');
            } else {
                return $this->_error('复制失败');
            }
        }
        
        return $this->_error('请求方式错误');
    }
}
