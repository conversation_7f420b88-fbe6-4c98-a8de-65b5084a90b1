<?php
/**
 * 迅排设计API客户端工厂类
 */

namespace app\common\service;

use think\facade\Config;
use think\facade\Env;

class PosterApiFactory
{
    private static $instance = null;
    private static $config = null;
    
    /**
     * 获取API客户端实例（单例模式）
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new PosterApiClient(self::getConfig());
        }
        
        return self::$instance;
    }
    
    /**
     * 创建新的API客户端实例
     */
    public static function create($config = [])
    {
        $defaultConfig = self::getConfig();
        $mergedConfig = array_merge($defaultConfig, $config);
        
        return new PosterApiClient($mergedConfig);
    }
    
    /**
     * 获取配置
     */
    private static function getConfig()
    {
        if (self::$config === null) {
            self::$config = [
                'base_url' => Env::get('poster.api_url', 'http://localhost:7001'),
                'timeout' => Env::get('poster.timeout', 30),
                'retry_times' => Env::get('poster.retry_times', 3),
                'retry_delay' => Env::get('poster.retry_delay', 1000),
                'cache_enabled' => Env::get('poster.cache_enabled', true),
                'cache_ttl' => Env::get('poster.cache_ttl', 600),
            ];
        }
        
        return self::$config;
    }
    
    /**
     * 更新配置
     */
    public static function updateConfig($config)
    {
        self::$config = array_merge(self::getConfig(), $config);
        self::$instance = null; // 重置实例，下次获取时会使用新配置
    }
    
    /**
     * 测试API连接
     */
    public static function testConnection()
    {
        try {
            $client = self::getInstance();
            $response = $client->healthCheck();
            
            return [
                'success' => $response['code'] == 200,
                'message' => $response['message'] ?? 'Unknown error',
                'data' => $response['data'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取API状态信息
     */
    public static function getApiStatus()
    {
        $client = self::getInstance();
        $config = self::getConfig();
        
        $status = [
            'base_url' => $config['base_url'],
            'timeout' => $config['timeout'],
            'cache_enabled' => $config['cache_enabled'],
            'connection_test' => self::testConnection()
        ];
        
        return $status;
    }
}
