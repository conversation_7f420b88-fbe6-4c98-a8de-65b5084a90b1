# 动态参数模板系统测试验证指南

本文档将指导您如何测试和验证动态参数模板系统的功能，以及需要完成的前置工作。

## 📋 前置准备工作

### 1. 环境要求检查

确保您的环境满足以下要求：

```bash
# 检查PHP版本（需要 >= 7.0）
php -v

# 检查必需的PHP扩展
php -m | grep -E "(pdo|pdo_mysql|json|curl|mbstring|openssl)"

# 检查Composer
composer --version

# 检查MySQL
mysql --version
```

### 2. 数据库准备

```bash
# 1. 创建主数据库
mysql -u root -p
CREATE DATABASE likeshop CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 创建测试数据库
CREATE DATABASE likeshop_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 3. 确认数据库创建成功
SHOW DATABASES;
```

### 3. 项目配置

```bash
# 1. 复制环境配置文件
cp .env.example .env

# 2. 编辑配置文件
vim .env
```

**重要配置项：**
```env
# 数据库配置
DB_HOST=127.0.0.1
DB_NAME=likeshop
DB_USER=root
DB_PASS=your_password

# 测试数据库配置
TEST_DB_NAME=likeshop_test

# 迅排设计API配置
POSTER_API_URL=http://localhost:7001
POSTER_API_KEY=your-api-key-here
POSTER_TEST_API_KEY=test-api-key-for-development

# 开发环境配置
DEV_MOCK_API=true
```

### 4. 依赖安装

```bash
# 安装项目依赖
composer install

# 如果是开发环境，安装开发依赖
composer install --dev
```

### 5. 数据库迁移

```bash
# 方法1：使用部署脚本（推荐）
chmod +x deploy.sh
./deploy.sh dev install

# 方法2：手动执行迁移
mysql -u root -p likeshop < database/migrations/create_poster_tables.sql
mysql -u root -p likeshop_test < database/migrations/create_poster_tables.sql
```

## 🧪 测试验证步骤

### 第一阶段：基础环境测试

#### 1. 数据库连接测试

```bash
# 进入项目根目录
cd /path/to/likeshop-server

# 测试数据库连接
php -r "
require_once 'thinkphp/base.php';
\think\Config::set('database', include 'test/config/database.php');
try {
    \think\Db::query('SELECT 1');
    echo '数据库连接成功' . PHP_EOL;
} catch (Exception \$e) {
    echo '数据库连接失败: ' . \$e->getMessage() . PHP_EOL;
}
"
```

#### 2. 数据表验证

```sql
-- 连接到数据库
mysql -u root -p likeshop

-- 检查表是否创建成功
SHOW TABLES LIKE 'ls_poster_%';

-- 检查表结构
DESCRIBE ls_poster_template_configs;
DESCRIBE ls_poster_user_data;
DESCRIBE ls_poster_generation_records;

-- 检查示例数据
SELECT * FROM ls_poster_template_configs;
```

### 第二阶段：单元测试

```bash
# 进入测试目录
cd test

# 运行所有测试
phpunit

# 运行特定测试套件
phpunit --testsuite=Unit
phpunit --testsuite=Integration
phpunit --testsuite=Functional

# 运行特定测试文件
phpunit unit/ModelTest.php
phpunit unit/ApiClientTest.php
phpunit unit/LogicTest.php

# 生成测试覆盖率报告
phpunit --coverage-html coverage-html
```

**预期结果：**
- 所有测试应该通过（绿色）
- 测试覆盖率应该 > 80%
- 没有致命错误或异常

### 第三阶段：API接口测试

#### 1. 外部API测试（供迅排设计服务调用）

```bash
# 健康检查
curl -X GET "http://localhost/api/external/health" \
  -H "Authorization: Bearer test-api-key-for-development"

# 获取参数配置
curl -X GET "http://localhost/api/external/parameter-config/test-config-001" \
  -H "Authorization: Bearer test-api-key-for-development"

# 获取参数数据
curl -X GET "http://localhost/api/external/parameter-data/test-data-001" \
  -H "Authorization: Bearer test-api-key-for-development"

# 批量获取参数数据
curl -X POST "http://localhost/api/external/parameter-data/batch" \
  -H "Authorization: Bearer test-api-key-for-development" \
  -H "Content-Type: application/json" \
  -d '{"dataIds": ["test-data-001", "test-data-002"]}'
```

#### 2. 前端API测试

```bash
# 获取表单配置
curl -X GET "http://localhost/api/poster/config/test-config-001"

# 获取配置列表
curl -X GET "http://localhost/api/poster/configs?page=1&limit=10"

# 提交表单数据
curl -X POST "http://localhost/api/poster/submit" \
  -H "Content-Type: application/json" \
  -H "session-id: test-session-123" \
  -d '{
    "config_id": "test-config-001",
    "parameter_values": {
      "greeting": "你好，测试！"
    },
    "is_draft": true
  }'
```

### 第四阶段：功能验证

#### 1. 后台管理功能验证

**访问后台管理：**
1. 访问 `http://localhost/admin`
2. 使用管理员账号登录
3. 导航到"动态模板"菜单

**验证功能：**
- [ ] 模板列表显示正常
- [ ] 可以查看模板详情
- [ ] 可以解析模板并创建配置
- [ ] 配置列表显示正常
- [ ] 可以编辑配置参数
- [ ] 用户数据列表显示正常
- [ ] 可以查看数据详情

#### 2. 前端表单功能验证

**创建测试页面：**

```html
<!-- 创建 test_form.html -->
<!DOCTYPE html>
<html>
<head>
    <title>动态表单测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <h1>动态参数模板系统测试</h1>
        
        <!-- 配置选择 -->
        <div>
            <label>选择配置：</label>
            <select id="configSelect">
                <option value="">请选择配置</option>
            </select>
            <button onclick="loadConfig()">加载配置</button>
        </div>
        
        <!-- 动态表单 -->
        <div id="formContainer" style="display:none;">
            <h2>参数填写</h2>
            <form id="parameterForm">
                <div id="formFields"></div>
                <button type="button" onclick="submitForm()">提交</button>
                <button type="button" onclick="generatePreview()">生成预览</button>
                <button type="button" onclick="generateImage()">生成图片</button>
            </form>
        </div>
        
        <!-- 结果显示 -->
        <div id="resultContainer" style="display:none;">
            <h2>结果</h2>
            <div id="result"></div>
        </div>
    </div>

    <script>
        let currentDataId = null;
        
        // 加载配置列表
        async function loadConfigList() {
            try {
                const response = await axios.get('/api/poster/configs');
                const configs = response.data.data.lists;
                const select = document.getElementById('configSelect');
                
                configs.forEach(config => {
                    const option = document.createElement('option');
                    option.value = config.id;
                    option.textContent = config.config_name;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('加载配置列表失败:', error);
            }
        }
        
        // 加载配置详情
        async function loadConfig() {
            const configId = document.getElementById('configSelect').value;
            if (!configId) return;
            
            try {
                const response = await axios.get(`/api/poster/config/${configId}`);
                const config = response.data.data;
                
                renderForm(config.parameters);
                document.getElementById('formContainer').style.display = 'block';
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        }
        
        // 渲染表单
        function renderForm(parameters) {
            const container = document.getElementById('formFields');
            container.innerHTML = '';
            
            parameters.forEach(param => {
                const div = document.createElement('div');
                div.innerHTML = `
                    <label>${param.parameterLabel}${param.isRequired ? '*' : ''}:</label>
                    <input type="${param.parameterType === 'number' ? 'number' : 'text'}" 
                           name="${param.parameterName}" 
                           ${param.isRequired ? 'required' : ''}
                           value="${param.defaultValue || ''}">
                `;
                container.appendChild(div);
            });
        }
        
        // 提交表单
        async function submitForm() {
            const configId = document.getElementById('configSelect').value;
            const formData = new FormData(document.getElementById('parameterForm'));
            const parameterValues = {};
            
            for (let [key, value] of formData.entries()) {
                parameterValues[key] = value;
            }
            
            try {
                const response = await axios.post('/api/poster/submit', {
                    config_id: configId,
                    parameter_values: parameterValues,
                    is_draft: true
                }, {
                    headers: {
                        'session-id': 'test-session-' + Date.now()
                    }
                });
                
                currentDataId = response.data.data.id;
                showResult('表单提交成功', response.data.data);
            } catch (error) {
                console.error('提交失败:', error);
                showResult('提交失败', error.response?.data || error.message);
            }
        }
        
        // 生成预览
        async function generatePreview() {
            if (!currentDataId) {
                alert('请先提交表单');
                return;
            }
            
            try {
                const response = await axios.post(`/api/poster/preview/${currentDataId}`);
                showResult('预览生成成功', response.data.data);
            } catch (error) {
                console.error('预览生成失败:', error);
                showResult('预览生成失败', error.response?.data || error.message);
            }
        }
        
        // 生成图片
        async function generateImage() {
            if (!currentDataId) {
                alert('请先提交表单');
                return;
            }
            
            try {
                const response = await axios.post('/api/poster/generate', {
                    data_id: currentDataId,
                    width: 1242,
                    height: 2208,
                    quality: 0.9
                });
                showResult('图片生成成功', response.data.data);
            } catch (error) {
                console.error('图片生成失败:', error);
                showResult('图片生成失败', error.response?.data || error.message);
            }
        }
        
        // 显示结果
        function showResult(title, data) {
            document.getElementById('result').innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            document.getElementById('resultContainer').style.display = 'block';
        }
        
        // 页面加载时初始化
        loadConfigList();
    </script>
</body>
</html>
```

**验证步骤：**
1. 将上述HTML保存为测试文件
2. 在浏览器中打开
3. 选择配置并填写表单
4. 测试提交、预览、生成功能

### 第五阶段：性能和安全测试

#### 1. 性能测试

```bash
# 使用Apache Bench进行压力测试
ab -n 100 -c 10 "http://localhost/api/poster/configs"

# 使用curl测试响应时间
time curl -X GET "http://localhost/api/external/health" \
  -H "Authorization: Bearer test-api-key-for-development"
```

#### 2. 安全测试

```bash
# 测试无效API密钥
curl -X GET "http://localhost/api/external/health" \
  -H "Authorization: Bearer invalid-key"

# 测试SQL注入防护
curl -X GET "http://localhost/api/poster/config/'; DROP TABLE users; --"

# 测试XSS防护
curl -X POST "http://localhost/api/poster/submit" \
  -H "Content-Type: application/json" \
  -d '{"config_id": "<script>alert(1)</script>", "parameter_values": {}}'
```

## ✅ 验证清单

### 基础功能
- [ ] 数据库连接正常
- [ ] 数据表创建成功
- [ ] 示例数据插入成功
- [ ] 所有单元测试通过
- [ ] 所有集成测试通过

### API接口
- [ ] 外部API健康检查正常
- [ ] 外部API认证机制工作正常
- [ ] 前端API响应正常
- [ ] API错误处理正确

### 业务功能
- [ ] 模板管理功能正常
- [ ] 参数配置功能正常
- [ ] 动态表单生成正常
- [ ] 数据提交功能正常
- [ ] 预览生成功能正常
- [ ] 图片生成功能正常

### 安全性
- [ ] API密钥认证有效
- [ ] 频率限制工作正常
- [ ] 输入验证正确
- [ ] SQL注入防护有效
- [ ] XSS防护有效

## 🐛 常见问题排查

### 1. 数据库连接失败
```bash
# 检查数据库服务状态
systemctl status mysql

# 检查配置文件
cat .env | grep DB_

# 测试连接
mysql -h127.0.0.1 -uroot -p
```

### 2. API调用失败
```bash
# 检查Web服务器状态
systemctl status apache2  # 或 nginx

# 检查PHP错误日志
tail -f /var/log/php/error.log

# 检查应用日志
tail -f runtime/log/error.log
```

### 3. 测试失败
```bash
# 查看详细测试输出
phpunit --verbose

# 查看测试日志
cat test/logs/test.log

# 重新初始化测试环境
cd test
php -r "require 'tools/TestHelper.php'; Test\Tools\TestHelper::initTestEnvironment();"
```

## � 迅排设计服务集成测试

### 1. Mock模式测试（推荐用于开发）

```bash
# 在.env中启用Mock模式
DEV_MOCK_API=true

# 运行测试
cd test
phpunit integration/PosterApiTest.php
```

### 2. 真实API测试（需要迅排设计服务）

```bash
# 在.env中配置真实API
POSTER_API_URL=http://your-poster-service:7001
POSTER_API_KEY=your-real-api-key
DEV_MOCK_API=false

# 测试API连接
curl -X GET "http://your-poster-service:7001/health"

# 运行集成测试
phpunit integration/PosterApiTest.php --group=external
```

## 📊 监控和日志

### 1. 应用日志

```bash
# 查看应用错误日志
tail -f runtime/log/error.log

# 查看API调用日志
tail -f runtime/log/info.log | grep "PosterAPI"

# 查看测试日志
tail -f test/logs/test.log
```

### 2. 性能监控

```bash
# 查看数据库查询性能
mysql -u root -p -e "SHOW PROCESSLIST;"

# 查看PHP进程状态
ps aux | grep php

# 查看内存使用情况
free -h
```

### 3. 错误监控

```bash
# 查看PHP错误
tail -f /var/log/php/error.log

# 查看Web服务器错误
tail -f /var/log/apache2/error.log  # 或 /var/log/nginx/error.log

# 查看系统日志
tail -f /var/log/syslog
```

## �📞 技术支持

如果在测试过程中遇到问题，请：

1. 查看相关日志文件
2. 检查环境配置
3. 参考本文档的排查步骤
4. 联系技术支持团队

### 常用调试命令

```bash
# 检查PHP配置
php -i | grep -E "(memory_limit|max_execution_time|upload_max_filesize)"

# 检查扩展加载
php -m

# 检查Composer依赖
composer show

# 检查数据库状态
mysql -u root -p -e "SHOW STATUS LIKE 'Connections';"
```

---

**注意：** 在生产环境部署前，请确保所有测试都通过，并根据实际需求调整配置参数。
