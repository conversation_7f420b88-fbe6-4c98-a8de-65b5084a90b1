<?php
/**
 * 用户数据管理控制器
 */

namespace app\admin\controller;

use app\admin\logic\PosterDataLogic;

class PosterData extends AdminBase
{
    /**
     * 用户数据列表页面
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $get = $this->request->get();
            return $this->_success('', PosterDataLogic::getDataList($get));
        }
        
        return $this->fetch();
    }
    
    /**
     * 数据详情
     */
    public function detail()
    {
        $id = $this->request->param('id');
        $data = PosterDataLogic::getDataDetail($id);
        
        if (!$data) {
            return $this->_error('数据不存在');
        }
        
        if ($this->request->isAjax()) {
            return $this->_success('获取成功', $data);
        }
        
        $this->assign('data', $data);
        return $this->fetch();
    }
    
    /**
     * 删除数据
     */
    public function del()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            
            if (empty($id)) {
                return $this->_error('参数错误');
            }
            
            $result = PosterDataLogic::delData($id);
            if ($result) {
                return $this->_success('删除成功');
            } else {
                return $this->_error('删除失败');
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 批量删除数据
     */
    public function batchDel()
    {
        if ($this->request->isAjax()) {
            $ids = $this->request->post('ids');
            
            if (empty($ids) || !is_array($ids)) {
                return $this->_error('参数错误');
            }
            
            $result = PosterDataLogic::batchDelData($ids);
            if ($result) {
                return $this->_success('批量删除成功');
            } else {
                return $this->_error('批量删除失败');
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 生成预览
     */
    public function generatePreview()
    {
        if ($this->request->isAjax()) {
            $id = $this->request->post('id');
            
            if (empty($id)) {
                return $this->_error('参数错误');
            }
            
            try {
                $result = PosterDataLogic::generatePreview($id);
                if ($result) {
                    return $this->_success('预览生成成功', $result);
                } else {
                    return $this->_error('预览生成失败');
                }
            } catch (\Exception $e) {
                return $this->_error('预览生成失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 生成图片
     */
    public function generateImage()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'id' => 'require',
                'width' => 'integer|min:100|max:4000',
                'height' => 'integer|min:100|max:4000',
                'quality' => 'float|between:0.1,1.0'
            ]);
            
            if ($result !== true) {
                return $this->_error($result);
            }
            
            try {
                $result = PosterDataLogic::generateImage(
                    $post['id'],
                    $post['width'] ?? 1242,
                    $post['height'] ?? 2208,
                    $post['quality'] ?? 0.9
                );
                
                if ($result) {
                    return $this->_success('图片生成成功', $result);
                } else {
                    return $this->_error('图片生成失败');
                }
            } catch (\Exception $e) {
                return $this->_error('图片生成失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 批量生成图片
     */
    public function batchGenerate()
    {
        if ($this->request->isAjax()) {
            $post = $this->request->post();
            
            $result = $this->validate($post, [
                'ids' => 'require|array',
                'width' => 'integer|min:100|max:4000',
                'height' => 'integer|min:100|max:4000',
                'quality' => 'float|between:0.1,1.0'
            ]);
            
            if ($result !== true) {
                return $this->_error($result);
            }
            
            try {
                $result = PosterDataLogic::batchGenerateImages(
                    $post['ids'],
                    [
                        'width' => $post['width'] ?? 1242,
                        'height' => $post['height'] ?? 2208,
                        'quality' => $post['quality'] ?? 0.9
                    ]
                );
                
                if ($result) {
                    return $this->_success('批量生成任务已创建', $result);
                } else {
                    return $this->_error('批量生成失败');
                }
            } catch (\Exception $e) {
                return $this->_error('批量生成失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 查询批量任务状态
     */
    public function batchStatus()
    {
        if ($this->request->isAjax()) {
            $batchId = $this->request->get('batch_id');
            
            if (empty($batchId)) {
                return $this->_error('参数错误');
            }
            
            try {
                $result = PosterDataLogic::getBatchStatus($batchId);
                if ($result) {
                    return $this->_success('获取成功', $result);
                } else {
                    return $this->_error('获取失败');
                }
            } catch (\Exception $e) {
                return $this->_error('获取失败: ' . $e->getMessage());
            }
        }
        
        return $this->_error('请求方式错误');
    }
    
    /**
     * 导出数据
     */
    public function export()
    {
        $get = $this->request->get();
        
        try {
            $result = PosterDataLogic::exportData($get);
            if ($result) {
                return $this->_success('导出成功', $result);
            } else {
                return $this->_error('导出失败');
            }
        } catch (\Exception $e) {
            return $this->_error('导出失败: ' . $e->getMessage());
        }
    }
}
