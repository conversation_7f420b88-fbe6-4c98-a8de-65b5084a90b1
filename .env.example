# 动态参数模板系统环境配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# 应用配置
APP_DEBUG=true
APP_TRACE=false

# 数据库配置
DB_TYPE=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=likeshop
DB_USER=root
DB_PASS=root
DB_PREFIX=ls_
DB_CHARSET=utf8mb4

# 测试数据库配置
TEST_DB_NAME=likeshop_test
TEST_DB_PREFIX=ls_test_

# 迅排设计API配置
POSTER_API_URL=http://localhost:7001
POSTER_API_KEY=your-api-key-here
POSTER_TEST_API_KEY=test-api-key-for-development
POSTER_TIMEOUT=30
POSTER_RETRY_TIMES=3
POSTER_RETRY_DELAY=1000

# 缓存配置
POSTER_CACHE_ENABLED=true
POSTER_CACHE_TTL=600

# 频率限制配置
POSTER_RATE_LIMIT=1000
POSTER_RATE_LIMIT_WINDOW=3600

# 日志配置
POSTER_LOG_ENABLED=true
POSTER_LOG_LEVEL=info

# 文件上传配置
POSTER_UPLOAD_MAX_SIZE=10485760
POSTER_UPLOAD_PATH=uploads/poster/

# 图片生成配置
POSTER_DEFAULT_WIDTH=1242
POSTER_DEFAULT_HEIGHT=2208
POSTER_DEFAULT_QUALITY=0.9
POSTER_MAX_WIDTH=4000
POSTER_MAX_HEIGHT=4000

# 批量处理配置
POSTER_BATCH_MAX_ITEMS=50
POSTER_BATCH_TIMEOUT=300

# 数据清理配置
POSTER_AUTO_CLEANUP=true
POSTER_DRAFT_RETENTION_DAYS=7
POSTER_RECORD_RETENTION_DAYS=30

# 安全配置
POSTER_ENABLE_IP_WHITELIST=false
POSTER_IP_WHITELIST=127.0.0.1,::1
POSTER_ENABLE_CORS=true
POSTER_CORS_ORIGINS=*

# 监控配置
POSTER_ENABLE_METRICS=true
POSTER_METRICS_RETENTION_DAYS=30
POSTER_ERROR_RATE_THRESHOLD=0.05
POSTER_RESPONSE_TIME_THRESHOLD=5000

# Redis配置（可选）
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0

# 邮件配置（可选）
MAIL_TYPE=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=动态参数模板系统

# 队列配置（可选）
QUEUE_TYPE=database
QUEUE_TABLE=queue_jobs

# 文件存储配置（可选）
FILESYSTEM_DRIVER=local
FILESYSTEM_ROOT=uploads

# CDN配置（可选）
CDN_DOMAIN=
CDN_ENABLED=false

# 第三方服务配置（可选）
SENTRY_DSN=
SENTRY_ENABLED=false

# 性能监控配置（可选）
PERFORMANCE_MONITORING=false
SLOW_QUERY_TIME=1000

# 开发环境配置
DEV_MOCK_API=true
DEV_DEBUG_SQL=true
DEV_SHOW_ERROR_MSG=true
